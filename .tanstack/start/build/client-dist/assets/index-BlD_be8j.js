import{j as e}from"./main-CwWbxkbL.js";import{C as c,a as l,b as r,c as i,d as n}from"./card-3gZ0OZ8g.js";import{B as s}from"./badge-D2BSwmjM.js";import{c as a}from"./createLucideIcon-BIEp0n8n.js";import"./index-DFx3G0N8.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],t=a("sparkles",d);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],o=a("target",x);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],h=a("zap",m),g=function(){return e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold mb-2",children:"模型中心"}),e.jsx("p",{className:"text-muted-foreground",children:"探索中化集团先进的AI模型解决方案"})]}),e.jsxs(c,{className:"mb-8 border-2",children:[e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(t,{className:"h-5 w-5 text-primary"}),e.jsx(s,{variant:"default",className:"text-sm",children:"推荐模型"})]}),e.jsx(r,{className:"text-2xl",children:"Sinochem Xinghai 兴海大模型"}),e.jsx(i,{children:"中化兴海自主研发的企业级大语言模型，专为化工行业智能化转型而设计"})]}),e.jsxs(n,{children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[e.jsx(o,{className:"h-4 w-4"}),"核心能力"]}),e.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[e.jsx("li",{children:"• 化工领域专业知识理解与生成"}),e.jsx("li",{children:"• 安全规程智能分析与预警"}),e.jsx("li",{children:"• 生产流程优化建议"}),e.jsx("li",{children:"• 设备故障诊断与预测"}),e.jsx("li",{children:"• 供应链智能决策支持"})]})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[e.jsx(h,{className:"h-4 w-4"}),"技术特色"]}),e.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[e.jsx("li",{children:"• 千亿级参数规模，行业知识深度训练"}),e.jsx("li",{children:"• 多模态融合，支持文本、图像、数据"}),e.jsx("li",{children:"• 实时学习与知识更新机制"}),e.jsx("li",{children:"• 企业级安全与合规保障"}),e.jsx("li",{children:"• 私有化部署与API服务双模式"})]})]})]}),e.jsx("div",{className:"mt-6 pt-6 border-t",children:e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(s,{variant:"secondary",children:"化工安全"}),e.jsx(s,{variant:"secondary",children:"生产优化"}),e.jsx(s,{variant:"secondary",children:"质量管控"}),e.jsx(s,{variant:"secondary",children:"供应链"}),e.jsx(s,{variant:"secondary",children:"研发创新"})]})})]})]})]})};export{g as component};
