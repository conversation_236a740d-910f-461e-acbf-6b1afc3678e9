import{j as e,u as k,r as m}from"./main-CwWbxkbL.js";import{L as b,A as w,a as M,b as U,S as A}from"./loader-circle-Dp2wQmSW.js";import{C as d}from"./card-3gZ0OZ8g.js";import{B as p}from"./button-Cj4E9B3-.js";import{g as C,a as I}from"./user-B27oqa2p.js";import{u as L,a as _}from"./use-auth-qGQmbmGk.js";import{c as l}from"./createLucideIcon-BIEp0n8n.js";import{U as f}from"./user-COwkwMpV.js";import{M as S}from"./mail-zNMFm0Ui.js";import"./index-DFx3G0N8.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],z=l("arrow-left",T);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],R=l("building",B);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],$=l("calendar",E);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],q=l("phone",D);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],P=l("refresh-cw",H);function F(){const{user:s,loading:j,error:o,setUser:y,setLoading:h,setError:c}=L(),{user:t}=_(),N=k(),[i,x]=m.useState(!1),n=m.useCallback(async(v=!1)=>{if(s&&!v)return;const r=t||s;if(!r){c("未找到用户信息，请重新登录");return}try{x(!0),h(!0),c(null);let a=null;r.id?a=await C(r.id):r.dingTalkUnionId&&(a=await I(r.dingTalkUnionId)),a?y(a):c("用户信息不存在")}catch(a){c(a instanceof Error?a.message:"获取用户信息失败")}finally{h(!1),x(!1)}},[t?.id,t?.dingTalkUnionId,s]);m.useEffect(()=>{!s&&t&&n()},[t,s,n]);const g=()=>{N({to:"/ai"})},u=()=>{n(!0)};return j?e.jsx(d,{className:"p-8",children:e.jsxs("div",{className:"flex items-center justify-center h-32",children:[e.jsx(b,{className:"h-8 w-8 animate-spin"}),e.jsx("span",{className:"ml-2",children:"加载用户信息..."})]})}):o||!s?e.jsx(d,{className:"p-8",children:e.jsx("div",{className:"text-center text-red-600",children:e.jsx("p",{children:o||"用户信息不存在"})})}):e.jsxs(d,{className:"p-8",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-3",children:[e.jsxs(p,{variant:"outline",size:"sm",onClick:g,className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-4 w-4"}),"返回AI页面"]}),e.jsxs(p,{variant:"outline",size:"sm",onClick:u,disabled:i,className:"flex items-center gap-2",children:[e.jsx(P,{className:`h-4 w-4 ${i?"animate-spin":""}`}),i?"刷新中...":"刷新数据"]})]}),e.jsx("div",{className:"flex items-start justify-between mb-6",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs(w,{className:"h-24 w-24",children:[e.jsx(M,{src:s.avatar||"",alt:s.name||"用户"}),e.jsx(U,{className:"text-2xl bg-blue-500 text-white",children:s.name?s.name.slice(0,2):"用户"})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:s.name||"未设置姓名"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:s.isAdmin?"管理员":"普通用户"}),e.jsxs("p",{className:"text-green-600 font-semibold mt-1",children:["Token: ",s.token?.toLocaleString()]})]})]})}),e.jsx(A,{className:"my-6"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"姓名"]}),e.jsx("p",{className:"text-gray-900",children:s.name||"未设置"})]}),s.email&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"邮箱"]}),e.jsx("p",{className:"text-gray-900",children:s.email})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"手机号"]}),e.jsx("p",{className:"text-gray-900",children:s.mobile||"未设置"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"账户Token"]}),e.jsx("p",{className:"font-semibold text-green-600",children:s.token?.toLocaleString()})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"请求次数"]}),e.jsx("p",{className:"text-gray-900",children:s.requestTimes})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"创建时间"]}),e.jsx("p",{className:"text-gray-900",children:s.createdAt?new Date(s.createdAt).toLocaleDateString("zh-CN"):"未知"})]})]})]})}const ee=function(){return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-4xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"账号管理"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"管理您的个人信息和账号设置"})]}),e.jsx(F,{})]})})};export{ee as component};
