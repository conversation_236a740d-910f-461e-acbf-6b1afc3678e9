const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-Dqv_FIK3.js","assets/main-CwWbxkbL.js"])))=>i.map(i=>d[i]);
import{g as tf,r as f,j as d,R as ye,a as ua,b as nf,c as rf,u as da,_ as Co}from"./main-CwWbxkbL.js";import{t as sf,a as us,c as j}from"./index-DFx3G0N8.js";import{c as Q}from"./createLucideIcon-BIEp0n8n.js";import{L as Dr,u as Ve,c as Re,P as G,d as fa,e as We,f as of,S as af,A as ha,a as pa,b as ma}from"./loader-circle-Dp2wQmSW.js";import{c as cf,p as lf,u as xe,a as ga}from"./use-auth-qGQmbmGk.js";import{d as de,e as Z,f as ae,m as Le,h as uf,i as ds,u as df,j as ff}from"./user-B27oqa2p.js";import{u as X,c as Sn,a as va,B as oe,S as Gt,b as ya,d as xa}from"./button-Cj4E9B3-.js";import{U as hf}from"./user-COwkwMpV.js";import{B as pf}from"./badge-D2BSwmjM.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mf=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ba=Q("check",mf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gf=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],wa=Q("chevron-down",gf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vf=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ca=Q("chevron-right",vf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yf=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],xf=Q("chevron-up",yf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bf=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],wf=Q("circle",bf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cf=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Sf=Q("copy",Cf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tf=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Af=Q("file-text",Tf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pf=[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]],Ef=Q("image-plus",Pf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rf=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Mf=Q("log-out",Rf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Df=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Nf=Q("menu",Df);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jf=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],If=Q("message-square",jf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kf=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],Of=Q("panel-left-close",kf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _f=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]],Lf=Q("panel-left-open",_f);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Ff=Q("panel-left",Vf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],$f=Q("pen-line",Bf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Wf=Q("plus",Uf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Kf=Q("trash-2",zf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hf=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ln=Q("x",Hf);var sr,So;function Gf(){return So||(So=1,sr=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,s,o;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(s=r;s--!==0;)if(!e(t[s],n[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(o=Object.keys(t),r=o.length,r!==Object.keys(n).length)return!1;for(s=r;s--!==0;)if(!Object.prototype.hasOwnProperty.call(n,o[s]))return!1;for(s=r;s--!==0;){var i=o[s];if(!e(t[i],n[i]))return!1}return!0}return t!==t&&n!==n}),sr}var Yf=Gf();const Xf=tf(Yf),fs=f.createContext({});function hs(e){const t=f.useRef(null);return t.current===null&&(t.current=e()),t.current}const ps=typeof window<"u",Sa=ps?f.useLayoutEffect:f.useEffect,Vn=f.createContext(null);function ms(e,t){e.indexOf(t)===-1&&e.push(t)}function gs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const Me=(e,t,n)=>n>t?t:n<e?e:n;let vs=()=>{};const De={},Ta=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function Aa(e){return typeof e=="object"&&e!==null}const Pa=e=>/^0[^.\s]+$/u.test(e);function ys(e){let t;return()=>(t===void 0&&(t=e()),t)}const fe=e=>e,qf=(e,t)=>n=>t(e(n)),Yt=(...e)=>e.reduce(qf),Ot=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r};class xs{constructor(){this.subscriptions=[]}add(t){return ms(this.subscriptions,t),()=>gs(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let o=0;o<s;o++){const i=this.subscriptions[o];i&&i(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ce=e=>e*1e3,Se=e=>e/1e3;function Ea(e,t){return t?e*(1e3/t):0}const Ra=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Zf=1e-7,Jf=12;function Qf(e,t,n,r,s){let o,i,a=0;do i=t+(n-t)/2,o=Ra(i,r,s)-e,o>0?n=i:t=i;while(Math.abs(o)>Zf&&++a<Jf);return i}function Xt(e,t,n,r){if(e===t&&n===r)return fe;const s=o=>Qf(o,0,1,e,n);return o=>o===0||o===1?o:Ra(s(o),t,r)}const Ma=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Da=e=>t=>1-e(1-t),Na=Xt(.33,1.53,.69,.99),bs=Da(Na),ja=Ma(bs),Ia=e=>(e*=2)<1?.5*bs(e):.5*(2-Math.pow(2,-10*(e-1))),ws=e=>1-Math.sin(Math.acos(e)),ka=Da(ws),Oa=Ma(ws),eh=Xt(.42,0,1,1),th=Xt(0,0,.58,1),_a=Xt(.42,0,.58,1),nh=e=>Array.isArray(e)&&typeof e[0]!="number",La=e=>Array.isArray(e)&&typeof e[0]=="number",rh={linear:fe,easeIn:eh,easeInOut:_a,easeOut:th,circIn:ws,circInOut:Oa,circOut:ka,backIn:bs,backInOut:ja,backOut:Na,anticipate:Ia},sh=e=>typeof e=="string",To=e=>{if(La(e)){vs(e.length===4);const[t,n,r,s]=e;return Xt(t,n,r,s)}else if(sh(e))return rh[e];return e},on=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function oh(e,t){let n=new Set,r=new Set,s=!1,o=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function c(l){i.has(l)&&(u.schedule(l),e()),l(a)}const u={schedule:(l,h=!1,p=!1)=>{const v=p&&s?n:r;return h&&i.add(l),v.has(l)||v.add(l),l},cancel:l=>{r.delete(l),i.delete(l)},process:l=>{if(a=l,s){o=!0;return}s=!0,[n,r]=[r,n],n.forEach(c),n.clear(),s=!1,o&&(o=!1,u.process(l))}};return u}const ih=40;function Va(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,i=on.reduce((w,S)=>(w[S]=oh(o),w),{}),{setup:a,read:c,resolveKeyframes:u,preUpdate:l,update:h,preRender:p,render:m,postRender:v}=i,g=()=>{const w=De.useManualTiming?s.timestamp:performance.now();n=!1,De.useManualTiming||(s.delta=r?1e3/60:Math.max(Math.min(w-s.timestamp,ih),1)),s.timestamp=w,s.isProcessing=!0,a.process(s),c.process(s),u.process(s),l.process(s),h.process(s),p.process(s),m.process(s),v.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},y=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:on.reduce((w,S)=>{const T=i[S];return w[S]=(M,P=!1,E=!1)=>(n||y(),T.schedule(M,P,E)),w},{}),cancel:w=>{for(let S=0;S<on.length;S++)i[on[S]].cancel(w)},state:s,steps:i}}const{schedule:U,cancel:Fe,state:ee,steps:or}=Va(typeof requestAnimationFrame<"u"?requestAnimationFrame:fe,!0);let gn;function ah(){gn=void 0}const se={now:()=>(gn===void 0&&se.set(ee.isProcessing||De.useManualTiming?ee.timestamp:performance.now()),gn),set:e=>{gn=e,queueMicrotask(ah)}},Fa=e=>t=>typeof t=="string"&&t.startsWith(e),Cs=Fa("--"),ch=Fa("var(--"),Ss=e=>ch(e)?lh.test(e.split("/*")[0].trim()):!1,lh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,xt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},_t={...xt,transform:e=>Me(0,1,e)},an={...xt,default:1},Dt=e=>Math.round(e*1e5)/1e5,Ts=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function uh(e){return e==null}const dh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,As=(e,t)=>n=>!!(typeof n=="string"&&dh.test(n)&&n.startsWith(e)||t&&!uh(n)&&Object.prototype.hasOwnProperty.call(n,t)),Ba=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,o,i,a]=r.match(Ts);return{[e]:parseFloat(s),[t]:parseFloat(o),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},fh=e=>Me(0,255,e),ir={...xt,transform:e=>Math.round(fh(e))},Xe={test:As("rgb","red"),parse:Ba("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ir.transform(e)+", "+ir.transform(t)+", "+ir.transform(n)+", "+Dt(_t.transform(r))+")"};function hh(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Nr={test:As("#"),parse:hh,transform:Xe.transform},qt=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Oe=qt("deg"),Te=qt("%"),k=qt("px"),ph=qt("vh"),mh=qt("vw"),Ao={...Te,parse:e=>Te.parse(e)/100,transform:e=>Te.transform(e*100)},at={test:As("hsl","hue"),parse:Ba("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Te.transform(Dt(t))+", "+Te.transform(Dt(n))+", "+Dt(_t.transform(r))+")"},q={test:e=>Xe.test(e)||Nr.test(e)||at.test(e),parse:e=>Xe.test(e)?Xe.parse(e):at.test(e)?at.parse(e):Nr.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Xe.transform(e):at.transform(e),getAnimatableNone:e=>{const t=q.parse(e);return t.alpha=0,q.transform(t)}},gh=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function vh(e){return isNaN(e)&&typeof e=="string"&&(e.match(Ts)?.length||0)+(e.match(gh)?.length||0)>0}const $a="number",Ua="color",yh="var",xh="var(",Po="${}",bh=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Lt(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let o=0;const a=t.replace(bh,c=>(q.test(c)?(r.color.push(o),s.push(Ua),n.push(q.parse(c))):c.startsWith(xh)?(r.var.push(o),s.push(yh),n.push(c)):(r.number.push(o),s.push($a),n.push(parseFloat(c))),++o,Po)).split(Po);return{values:n,split:a,indexes:r,types:s}}function Wa(e){return Lt(e).values}function za(e){const{split:t,types:n}=Lt(e),r=t.length;return s=>{let o="";for(let i=0;i<r;i++)if(o+=t[i],s[i]!==void 0){const a=n[i];a===$a?o+=Dt(s[i]):a===Ua?o+=q.transform(s[i]):o+=s[i]}return o}}const wh=e=>typeof e=="number"?0:q.test(e)?q.getAnimatableNone(e):e;function Ch(e){const t=Wa(e);return za(e)(t.map(wh))}const Be={test:vh,parse:Wa,createTransformer:za,getAnimatableNone:Ch};function ar(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Sh({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,o=0,i=0;if(!t)s=o=i=n;else{const a=n<.5?n*(1+t):n+t-n*t,c=2*n-a;s=ar(c,a,e+1/3),o=ar(c,a,e),i=ar(c,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:r}}function Tn(e,t){return n=>n>0?t:e}const z=(e,t,n)=>e+(t-e)*n,cr=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},Th=[Nr,Xe,at],Ah=e=>Th.find(t=>t.test(e));function Eo(e){const t=Ah(e);if(!t)return!1;let n=t.parse(e);return t===at&&(n=Sh(n)),n}const Ro=(e,t)=>{const n=Eo(e),r=Eo(t);if(!n||!r)return Tn(e,t);const s={...n};return o=>(s.red=cr(n.red,r.red,o),s.green=cr(n.green,r.green,o),s.blue=cr(n.blue,r.blue,o),s.alpha=z(n.alpha,r.alpha,o),Xe.transform(s))},jr=new Set(["none","hidden"]);function Ph(e,t){return jr.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Eh(e,t){return n=>z(e,t,n)}function Ps(e){return typeof e=="number"?Eh:typeof e=="string"?Ss(e)?Tn:q.test(e)?Ro:Dh:Array.isArray(e)?Ka:typeof e=="object"?q.test(e)?Ro:Rh:Tn}function Ka(e,t){const n=[...e],r=n.length,s=e.map((o,i)=>Ps(o)(o,t[i]));return o=>{for(let i=0;i<r;i++)n[i]=s[i](o);return n}}function Rh(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=Ps(e[s])(e[s],t[s]));return s=>{for(const o in r)n[o]=r[o](s);return n}}function Mh(e,t){const n=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],i=e.indexes[o][r[o]],a=e.values[i]??0;n[s]=a,r[o]++}return n}const Dh=(e,t)=>{const n=Be.createTransformer(t),r=Lt(e),s=Lt(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?jr.has(e)&&!s.values.length||jr.has(t)&&!r.values.length?Ph(e,t):Yt(Ka(Mh(r,s),s.values),n):Tn(e,t)};function Ha(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?z(e,t,n):Ps(e)(e,t)}const Nh=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>U.update(t,n),stop:()=>Fe(t),now:()=>ee.isProcessing?ee.timestamp:se.now()}},Ga=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let o=0;o<s;o++)r+=Math.round(e(o/(s-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},An=2e4;function Es(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<An;)t+=n,r=e.next(t);return t>=An?1/0:t}function jh(e,t=100,n){const r=n({...e,keyframes:[0,t]}),s=Math.min(Es(r),An);return{type:"keyframes",ease:o=>r.next(s*o).value/t,duration:Se(s)}}const Ih=5;function Ya(e,t,n){const r=Math.max(t-Ih,0);return Ea(n-e(r),t-r)}const H={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},lr=.001;function kh({duration:e=H.duration,bounce:t=H.bounce,velocity:n=H.velocity,mass:r=H.mass}){let s,o,i=1-t;i=Me(H.minDamping,H.maxDamping,i),e=Me(H.minDuration,H.maxDuration,Se(e)),i<1?(s=u=>{const l=u*i,h=l*e,p=l-n,m=Ir(u,i),v=Math.exp(-h);return lr-p/m*v},o=u=>{const h=u*i*e,p=h*n+n,m=Math.pow(i,2)*Math.pow(u,2)*e,v=Math.exp(-h),g=Ir(Math.pow(u,2),i);return(-s(u)+lr>0?-1:1)*((p-m)*v)/g}):(s=u=>{const l=Math.exp(-u*e),h=(u-n)*e+1;return-lr+l*h},o=u=>{const l=Math.exp(-u*e),h=(n-u)*(e*e);return l*h});const a=5/e,c=_h(s,o,a);if(e=Ce(e),isNaN(c))return{stiffness:H.stiffness,damping:H.damping,duration:e};{const u=Math.pow(c,2)*r;return{stiffness:u,damping:i*2*Math.sqrt(r*u),duration:e}}}const Oh=12;function _h(e,t,n){let r=n;for(let s=1;s<Oh;s++)r=r-e(r)/t(r);return r}function Ir(e,t){return e*Math.sqrt(1-t*t)}const Lh=["duration","bounce"],Vh=["stiffness","damping","mass"];function Mo(e,t){return t.some(n=>e[n]!==void 0)}function Fh(e){let t={velocity:H.velocity,stiffness:H.stiffness,damping:H.damping,mass:H.mass,isResolvedFromDuration:!1,...e};if(!Mo(e,Vh)&&Mo(e,Lh))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,o=2*Me(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:H.mass,stiffness:s,damping:o}}else{const n=kh(e);t={...t,...n,mass:H.mass},t.isResolvedFromDuration=!0}return t}function Pn(e=H.visualDuration,t=H.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const o=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:c,damping:u,mass:l,duration:h,velocity:p,isResolvedFromDuration:m}=Fh({...n,velocity:-Se(n.velocity||0)}),v=p||0,g=u/(2*Math.sqrt(c*l)),y=i-o,x=Se(Math.sqrt(c/l)),b=Math.abs(y)<5;r||(r=b?H.restSpeed.granular:H.restSpeed.default),s||(s=b?H.restDelta.granular:H.restDelta.default);let w;if(g<1){const T=Ir(x,g);w=M=>{const P=Math.exp(-g*x*M);return i-P*((v+g*x*y)/T*Math.sin(T*M)+y*Math.cos(T*M))}}else if(g===1)w=T=>i-Math.exp(-x*T)*(y+(v+x*y)*T);else{const T=x*Math.sqrt(g*g-1);w=M=>{const P=Math.exp(-g*x*M),E=Math.min(T*M,300);return i-P*((v+g*x*y)*Math.sinh(E)+T*y*Math.cosh(E))/T}}const S={calculatedDuration:m&&h||null,next:T=>{const M=w(T);if(m)a.done=T>=h;else{let P=T===0?v:0;g<1&&(P=T===0?Ce(v):Ya(w,T,M));const E=Math.abs(P)<=r,R=Math.abs(i-M)<=s;a.done=E&&R}return a.value=a.done?i:M,a},toString:()=>{const T=Math.min(Es(S),An),M=Ga(P=>S.next(T*P).value,T,30);return T+"ms "+M},toTransition:()=>{}};return S}Pn.applyToOptions=e=>{const t=jh(e,100,Pn);return e.ease=t.ease,e.duration=Ce(t.duration),e.type="keyframes",e};function kr({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:i,min:a,max:c,restDelta:u=.5,restSpeed:l}){const h=e[0],p={done:!1,value:h},m=E=>a!==void 0&&E<a||c!==void 0&&E>c,v=E=>a===void 0?c:c===void 0||Math.abs(a-E)<Math.abs(c-E)?a:c;let g=n*t;const y=h+g,x=i===void 0?y:i(y);x!==y&&(g=x-h);const b=E=>-g*Math.exp(-E/r),w=E=>x+b(E),S=E=>{const R=b(E),N=w(E);p.done=Math.abs(R)<=u,p.value=p.done?x:N};let T,M;const P=E=>{m(p.value)&&(T=E,M=Pn({keyframes:[p.value,v(p.value)],velocity:Ya(w,E,p.value),damping:s,stiffness:o,restDelta:u,restSpeed:l}))};return P(0),{calculatedDuration:null,next:E=>{let R=!1;return!M&&T===void 0&&(R=!0,S(E),P(E)),T!==void 0&&E>=T?M.next(E-T):(!R&&S(E),p)}}}function Bh(e,t,n){const r=[],s=n||De.mix||Ha,o=e.length-1;for(let i=0;i<o;i++){let a=s(e[i],e[i+1]);if(t){const c=Array.isArray(t)?t[i]||fe:t;a=Yt(c,a)}r.push(a)}return r}function $h(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const o=e.length;if(vs(o===t.length),o===1)return()=>t[0];if(o===2&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=Bh(t,r,s),c=a.length,u=l=>{if(i&&l<e[0])return t[0];let h=0;if(c>1)for(;h<e.length-2&&!(l<e[h+1]);h++);const p=Ot(e[h],e[h+1],l);return a[h](p)};return n?l=>u(Me(e[0],e[o-1],l)):u}function Uh(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=Ot(0,t,r);e.push(z(n,1,s))}}function Wh(e){const t=[0];return Uh(t,e.length-1),t}function zh(e,t){return e.map(n=>n*t)}function Kh(e,t){return e.map(()=>t||_a).splice(0,e.length-1)}function Nt({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=nh(r)?r.map(To):To(r),o={done:!1,value:t[0]},i=zh(n&&n.length===t.length?n:Wh(t),e),a=$h(i,t,{ease:Array.isArray(s)?s:Kh(t,s)});return{calculatedDuration:e,next:c=>(o.value=a(c),o.done=c>=e,o)}}const Hh=e=>e!==null;function Rs(e,{repeat:t,repeatType:n="loop"},r,s=1){const o=e.filter(Hh),a=s<0||t&&n!=="loop"&&t%2===1?0:o.length-1;return!a||r===void 0?o[a]:r}const Gh={decay:kr,inertia:kr,tween:Nt,keyframes:Nt,spring:Pn};function Xa(e){typeof e.type=="string"&&(e.type=Gh[e.type])}class Ms{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const Yh=e=>e/100;class Ds extends Ms{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==se.now()&&this.tick(se.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;Xa(t);const{type:n=Nt,repeat:r=0,repeatDelay:s=0,repeatType:o,velocity:i=0}=t;let{keyframes:a}=t;const c=n||Nt;c!==Nt&&typeof a[0]!="number"&&(this.mixKeyframes=Yt(Yh,Ha(a[0],a[1])),a=[0,100]);const u=c({...t,keyframes:a});o==="mirror"&&(this.mirroredGenerator=c({...t,keyframes:[...a].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=Es(u));const{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+s,this.totalDuration=this.resolvedDuration*(r+1)-s,this.generator=u}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:r,totalDuration:s,mixKeyframes:o,mirroredGenerator:i,resolvedDuration:a,calculatedDuration:c}=this;if(this.startTime===null)return r.next(0);const{delay:u=0,keyframes:l,repeat:h,repeatType:p,repeatDelay:m,type:v,onUpdate:g,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>s;this.currentTime=Math.max(x,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let w=this.currentTime,S=r;if(h){const E=Math.min(this.currentTime,s)/a;let R=Math.floor(E),N=E%1;!N&&E>=1&&(N=1),N===1&&R--,R=Math.min(R,h+1),!!(R%2)&&(p==="reverse"?(N=1-N,m&&(N-=m/a)):p==="mirror"&&(S=i)),w=Me(0,1,N)*a}const T=b?{done:!1,value:l[0]}:S.next(w);o&&(T.value=o(T.value));let{done:M}=T;!b&&c!==null&&(M=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const P=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return P&&v!==kr&&(T.value=Rs(l,this.options,y,this.speed)),g&&g(T.value),P&&this.finish(),T}then(t,n){return this.finished.then(t,n)}get duration(){return Se(this.calculatedDuration)}get time(){return Se(this.currentTime)}set time(t){t=Ce(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(se.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Se(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Nh,startTime:n}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(se.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Xh(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const qe=e=>e*180/Math.PI,Or=e=>{const t=qe(Math.atan2(e[1],e[0]));return _r(t)},qh={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Or,rotateZ:Or,skewX:e=>qe(Math.atan(e[1])),skewY:e=>qe(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},_r=e=>(e=e%360,e<0&&(e+=360),e),Do=Or,No=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),jo=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Zh={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:No,scaleY:jo,scale:e=>(No(e)+jo(e))/2,rotateX:e=>_r(qe(Math.atan2(e[6],e[5]))),rotateY:e=>_r(qe(Math.atan2(-e[2],e[0]))),rotateZ:Do,rotate:Do,skewX:e=>qe(Math.atan(e[4])),skewY:e=>qe(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Lr(e){return e.includes("scale")?1:0}function Vr(e,t){if(!e||e==="none")return Lr(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,s;if(n)r=Zh,s=n;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=qh,s=a}if(!s)return Lr(t);const o=r[t],i=s[1].split(",").map(Qh);return typeof o=="function"?o(i):i[o]}const Jh=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Vr(n,t)};function Qh(e){return parseFloat(e.trim())}const bt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],wt=new Set(bt),Io=e=>e===xt||e===k,ep=new Set(["x","y","z"]),tp=bt.filter(e=>!ep.has(e));function np(e){const t=[];return tp.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Ze={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Vr(t,"x"),y:(e,{transform:t})=>Vr(t,"y")};Ze.translateX=Ze.x;Ze.translateY=Ze.y;const Je=new Set;let Fr=!1,Br=!1,$r=!1;function qa(){if(Br){const e=Array.from(Je).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=np(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([o,i])=>{r.getValue(o)?.set(i)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Br=!1,Fr=!1,Je.forEach(e=>e.complete($r)),Je.clear()}function Za(){Je.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Br=!0)})}function rp(){$r=!0,Za(),qa(),$r=!1}class Ns{constructor(t,n,r,s,o,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=o,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(Je.add(this),Fr||(Fr=!0,U.read(Za),U.resolveKeyframes(qa))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;if(t[0]===null){const o=s?.get(),i=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const a=r.readValue(n,i);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=i),s&&o===void 0&&s.set(t[0])}Xh(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Je.delete(this)}cancel(){this.state==="scheduled"&&(Je.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const sp=e=>e.startsWith("--");function op(e,t,n){sp(t)?e.style.setProperty(t,n):e.style[t]=n}const ip=ys(()=>window.ScrollTimeline!==void 0),ap={};function cp(e,t){const n=ys(e);return()=>ap[t]??n()}const Ja=cp(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Et=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ko={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Et([0,.65,.55,1]),circOut:Et([.55,0,1,.45]),backIn:Et([.31,.01,.66,-.59]),backOut:Et([.33,1.53,.69,.99])};function Qa(e,t){if(e)return typeof e=="function"?Ja()?Ga(e,t):"ease-out":La(e)?Et(e):Array.isArray(e)?e.map(n=>Qa(n,t)||ko.easeOut):ko[e]}function lp(e,t,n,{delay:r=0,duration:s=300,repeat:o=0,repeatType:i="loop",ease:a="easeOut",times:c}={},u=void 0){const l={[t]:n};c&&(l.offset=c);const h=Qa(a,s);Array.isArray(h)&&(l.easing=h);const p={delay:r,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"};return u&&(p.pseudoElement=u),e.animate(l,p)}function ec(e){return typeof e=="function"&&"applyToOptions"in e}function up({type:e,...t}){return ec(e)&&Ja()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class dp extends Ms{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:r,keyframes:s,pseudoElement:o,allowFlatten:i=!1,finalKeyframe:a,onComplete:c}=t;this.isPseudoElement=!!o,this.allowFlatten=i,this.options=t,vs(typeof t.type!="string");const u=up(t);this.animation=lp(n,r,s,u,o),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const l=Rs(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(l):op(n,r,l),this.animation.cancel()}c?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return Se(Number(t))}get time(){return Se(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Ce(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ip()?(this.animation.timeline=t,fe):n(this)}}const tc={anticipate:Ia,backInOut:ja,circInOut:Oa};function fp(e){return e in tc}function hp(e){typeof e.ease=="string"&&fp(e.ease)&&(e.ease=tc[e.ease])}const Oo=10;class pp extends dp{constructor(t){hp(t),Xa(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:r,onComplete:s,element:o,...i}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const a=new Ds({...i,autoplay:!1}),c=Ce(this.finishedTime??this.time);n.setWithVelocity(a.sample(c-Oo).value,a.sample(c).value,Oo),a.stop()}}const _o=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Be.test(e)||e==="0")&&!e.startsWith("url("));function mp(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function gp(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],i=_o(s,t),a=_o(o,t);return!i||!a?!1:mp(e)||(n==="spring"||ec(n))&&r}function Ur(e){e.duration=0,e.type}const vp=new Set(["opacity","clipPath","filter","transform"]),yp=ys(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function xp(e){const{motionValue:t,name:n,repeatDelay:r,repeatType:s,damping:o,type:i}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:c,transformTemplate:u}=t.owner.getProps();return yp()&&n&&vp.has(n)&&(n!=="transform"||!u)&&!c&&!r&&s!=="mirror"&&o!==0&&i!=="inertia"}const bp=40;class wp extends Ms{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:i="loop",keyframes:a,name:c,motionValue:u,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=se.now();const p={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:o,repeatType:i,name:c,motionValue:u,element:l,...h},m=l?.KeyframeResolver||Ns;this.keyframeResolver=new m(a,(v,g,y)=>this.onKeyframesResolved(v,g,p,!y),c,u,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,r,s){this.keyframeResolver=void 0;const{name:o,type:i,velocity:a,delay:c,isHandoff:u,onUpdate:l}=r;this.resolvedAt=se.now(),gp(t,o,i,a)||((De.instantAnimations||!c)&&l?.(Rs(t,r,n)),t[0]=t[t.length-1],Ur(r),r.repeat=0);const p={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>bp?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...r,keyframes:t},m=!u&&xp(p)?new pp({...p,element:p.motionValue.owner.current}):new Ds(p);m.finished.then(()=>this.notifyFinished()).catch(fe),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),rp()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Cp=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Sp(e){const t=Cp.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function nc(e,t,n=1){const[r,s]=Sp(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const i=o.trim();return Ta(i)?parseFloat(i):i}return Ss(s)?nc(s,t,n+1):s}function js(e,t){return e?.[t]??e?.default??e}const rc=new Set(["width","height","top","left","right","bottom",...bt]),Tp={test:e=>e==="auto",parse:e=>e},sc=e=>t=>t.test(e),oc=[xt,k,Te,Oe,mh,ph,Tp],Lo=e=>oc.find(sc(e));function Ap(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Pa(e):!0}const Pp=new Set(["brightness","contrast","saturate","opacity"]);function Ep(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ts)||[];if(!r)return e;const s=n.replace(r,"");let o=Pp.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+s+")"}const Rp=/\b([a-z-]*)\(.*?\)/gu,Wr={...Be,getAnimatableNone:e=>{const t=e.match(Rp);return t?t.map(Ep).join(" "):e}},Vo={...xt,transform:Math.round},Mp={rotate:Oe,rotateX:Oe,rotateY:Oe,rotateZ:Oe,scale:an,scaleX:an,scaleY:an,scaleZ:an,skew:Oe,skewX:Oe,skewY:Oe,distance:k,translateX:k,translateY:k,translateZ:k,x:k,y:k,z:k,perspective:k,transformPerspective:k,opacity:_t,originX:Ao,originY:Ao,originZ:k},Is={borderWidth:k,borderTopWidth:k,borderRightWidth:k,borderBottomWidth:k,borderLeftWidth:k,borderRadius:k,radius:k,borderTopLeftRadius:k,borderTopRightRadius:k,borderBottomRightRadius:k,borderBottomLeftRadius:k,width:k,maxWidth:k,height:k,maxHeight:k,top:k,right:k,bottom:k,left:k,padding:k,paddingTop:k,paddingRight:k,paddingBottom:k,paddingLeft:k,margin:k,marginTop:k,marginRight:k,marginBottom:k,marginLeft:k,backgroundPositionX:k,backgroundPositionY:k,...Mp,zIndex:Vo,fillOpacity:_t,strokeOpacity:_t,numOctaves:Vo},Dp={...Is,color:q,backgroundColor:q,outlineColor:q,fill:q,stroke:q,borderColor:q,borderTopColor:q,borderRightColor:q,borderBottomColor:q,borderLeftColor:q,filter:Wr,WebkitFilter:Wr},ic=e=>Dp[e];function ac(e,t){let n=ic(e);return n!==Wr&&(n=Be),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Np=new Set(["auto","none","0"]);function jp(e,t,n){let r=0,s;for(;r<e.length&&!s;){const o=e[r];typeof o=="string"&&!Np.has(o)&&Lt(o).values.length&&(s=e[r]),r++}if(s&&n)for(const o of t)e[o]=ac(n,s)}class Ip extends Ns{constructor(t,n,r,s,o){super(t,n,r,s,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let c=0;c<t.length;c++){let u=t[c];if(typeof u=="string"&&(u=u.trim(),Ss(u))){const l=nc(u,n.current);l!==void 0&&(t[c]=l),c===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!rc.has(r)||t.length!==2)return;const[s,o]=t,i=Lo(s),a=Lo(o);if(i!==a)if(Io(i)&&Io(a))for(let c=0;c<t.length;c++){const u=t[c];typeof u=="string"&&(t[c]=parseFloat(u))}else Ze[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)(t[s]===null||Ap(t[s]))&&r.push(s);r.length&&jp(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ze[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const s=t.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=r.length-1,i=r[o];r[o]=Ze[n](t.measureViewportBox(),window.getComputedStyle(t.current)),i!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,c])=>{t.getValue(a).set(c)}),this.resolveNoneKeyframes()}}function kp(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let r=document;const s=n?.[e]??r.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}const cc=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function lc(e){return Aa(e)&&"offsetHeight"in e}const Fo=30,Op=e=>!isNaN(parseFloat(e));class _p{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=r=>{const s=se.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=se.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Op(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new xs);const r=this.events[t].add(n);return t==="change"?()=>{r(),U.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=se.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Fo)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Fo);return Ea(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function mt(e,t){return new _p(e,t)}const{schedule:ks}=Va(queueMicrotask,!1),pe={x:!1,y:!1};function uc(){return pe.x||pe.y}function Lp(e){return e==="x"||e==="y"?pe[e]?null:(pe[e]=!0,()=>{pe[e]=!1}):pe.x||pe.y?null:(pe.x=pe.y=!0,()=>{pe.x=pe.y=!1})}function dc(e,t){const n=kp(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function Bo(e){return!(e.pointerType==="touch"||uc())}function Vp(e,t,n={}){const[r,s,o]=dc(e,n),i=a=>{if(!Bo(a))return;const{target:c}=a,u=t(c,a);if(typeof u!="function"||!c)return;const l=h=>{Bo(h)&&(u(h),c.removeEventListener("pointerleave",l))};c.addEventListener("pointerleave",l,s)};return r.forEach(a=>{a.addEventListener("pointerenter",i,s)}),o}const fc=(e,t)=>t?e===t?!0:fc(e,t.parentElement):!1,Os=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,Fp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Bp(e){return Fp.has(e.tagName)||e.tabIndex!==-1}const vn=new WeakSet;function $o(e){return t=>{t.key==="Enter"&&e(t)}}function ur(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const $p=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=$o(()=>{if(vn.has(n))return;ur(n,"down");const s=$o(()=>{ur(n,"up")}),o=()=>ur(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",o,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Uo(e){return Os(e)&&!uc()}function Up(e,t,n={}){const[r,s,o]=dc(e,n),i=a=>{const c=a.currentTarget;if(!Uo(a))return;vn.add(c);const u=t(c,a),l=(m,v)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",p),vn.has(c)&&vn.delete(c),Uo(m)&&typeof u=="function"&&u(m,{success:v})},h=m=>{l(m,c===window||c===document||n.useGlobalTarget||fc(c,m.target))},p=m=>{l(m,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",p,s)};return r.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",i,s),lc(a)&&(a.addEventListener("focus",u=>$p(u,s)),!Bp(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function hc(e){return Aa(e)&&"ownerSVGElement"in e}function Wp(e){return hc(e)&&e.tagName==="svg"}const te=e=>!!(e&&e.getVelocity),zp=[...oc,q,Be],Kp=e=>zp.find(sc(e)),_s=f.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Hp extends f.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,s=lc(r)&&r.offsetWidth||0,o=this.props.sizeRef.current;o.height=n.offsetHeight||0,o.width=n.offsetWidth||0,o.top=n.offsetTop,o.left=n.offsetLeft,o.right=s-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Gp({children:e,isPresent:t,anchorX:n,root:r}){const s=f.useId(),o=f.useRef(null),i=f.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=f.useContext(_s);return f.useInsertionEffect(()=>{const{width:c,height:u,top:l,left:h,right:p}=i.current;if(t||!o.current||!c||!u)return;const m=n==="left"?`left: ${h}`:`right: ${p}`;o.current.dataset.motionPopId=s;const v=document.createElement("style");a&&(v.nonce=a);const g=r??document.head;return g.appendChild(v),v.sheet&&v.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${c}px !important;
            height: ${u}px !important;
            ${m}px !important;
            top: ${l}px !important;
          }
        `),()=>{g.contains(v)&&g.removeChild(v)}},[t]),d.jsx(Hp,{isPresent:t,childRef:o,sizeRef:i,children:f.cloneElement(e,{ref:o})})}const Yp=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:o,mode:i,anchorX:a,root:c})=>{const u=hs(Xp),l=f.useId();let h=!0,p=f.useMemo(()=>(h=!1,{id:l,initial:t,isPresent:n,custom:s,onExitComplete:m=>{u.set(m,!0);for(const v of u.values())if(!v)return;r&&r()},register:m=>(u.set(m,!1),()=>u.delete(m))}),[n,u,r]);return o&&h&&(p={...p}),f.useMemo(()=>{u.forEach((m,v)=>u.set(v,!1))},[n]),f.useEffect(()=>{!n&&!u.size&&r&&r()},[n]),i==="popLayout"&&(e=d.jsx(Gp,{isPresent:n,anchorX:a,root:c,children:e})),d.jsx(Vn.Provider,{value:p,children:e})};function Xp(){return new Map}function pc(e=!0){const t=f.useContext(Vn);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,o=f.useId();f.useEffect(()=>{if(e)return s(o)},[e]);const i=f.useCallback(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,i]:[!0]}const cn=e=>e.key||"";function Wo(e){const t=[];return f.Children.forEach(e,n=>{f.isValidElement(n)&&t.push(n)}),t}const mc=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:o="sync",propagate:i=!1,anchorX:a="left",root:c})=>{const[u,l]=pc(i),h=f.useMemo(()=>Wo(e),[e]),p=i&&!u?[]:h.map(cn),m=f.useRef(!0),v=f.useRef(h),g=hs(()=>new Map),[y,x]=f.useState(h),[b,w]=f.useState(h);Sa(()=>{m.current=!1,v.current=h;for(let M=0;M<b.length;M++){const P=cn(b[M]);p.includes(P)?g.delete(P):g.get(P)!==!0&&g.set(P,!1)}},[b,p.length,p.join("-")]);const S=[];if(h!==y){let M=[...h];for(let P=0;P<b.length;P++){const E=b[P],R=cn(E);p.includes(R)||(M.splice(P,0,E),S.push(E))}return o==="wait"&&S.length&&(M=S),w(Wo(M)),x(h),null}const{forceRender:T}=f.useContext(fs);return d.jsx(d.Fragment,{children:b.map(M=>{const P=cn(M),E=i&&!u?!1:h===b||p.includes(P),R=()=>{if(g.has(P))g.set(P,!0);else return;let N=!0;g.forEach(O=>{O||(N=!1)}),N&&(T?.(),w(v.current),i&&l?.(),r&&r())};return d.jsx(Yp,{isPresent:E,initial:!m.current||n?void 0:!1,custom:t,presenceAffectsLayout:s,mode:o,root:c,onExitComplete:E?void 0:R,anchorX:a,children:M},P)})})},gc=f.createContext({strict:!1}),zo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},gt={};for(const e in zo)gt[e]={isEnabled:t=>zo[e].some(n=>!!t[n])};function qp(e){for(const t in e)gt[t]={...gt[t],...e[t]}}const Zp=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function En(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Zp.has(e)}let vc=e=>!En(e);function Jp(e){typeof e=="function"&&(vc=t=>t.startsWith("on")?!En(t):e(t))}try{Jp(require("@emotion/is-prop-valid").default)}catch{}function Qp(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(vc(s)||n===!0&&En(s)||!t&&!En(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}const Fn=f.createContext({});function Bn(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function Vt(e){return typeof e=="string"||Array.isArray(e)}const Ls=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Vs=["initial",...Ls];function $n(e){return Bn(e.animate)||Vs.some(t=>Vt(e[t]))}function yc(e){return!!($n(e)||e.variants)}function em(e,t){if($n(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Vt(n)?n:void 0,animate:Vt(r)?r:void 0}}return e.inherit!==!1?t:{}}function tm(e){const{initial:t,animate:n}=em(e,f.useContext(Fn));return f.useMemo(()=>({initial:t,animate:n}),[Ko(t),Ko(n)])}function Ko(e){return Array.isArray(e)?e.join(" "):e}const Ft={};function nm(e){for(const t in e)Ft[t]=e[t],Cs(t)&&(Ft[t].isCSSVariable=!0)}function xc(e,{layout:t,layoutId:n}){return wt.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ft[e]||e==="opacity")}const rm={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sm=bt.length;function om(e,t,n){let r="",s=!0;for(let o=0;o<sm;o++){const i=bt[o],a=e[i];if(a===void 0)continue;let c=!0;if(typeof a=="number"?c=a===(i.startsWith("scale")?1:0):c=parseFloat(a)===0,!c||n){const u=cc(a,Is[i]);if(!c){s=!1;const l=rm[i]||i;r+=`${l}(${u}) `}n&&(t[i]=u)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function Fs(e,t,n){const{style:r,vars:s,transformOrigin:o}=e;let i=!1,a=!1;for(const c in t){const u=t[c];if(wt.has(c)){i=!0;continue}else if(Cs(c)){s[c]=u;continue}else{const l=cc(u,Is[c]);c.startsWith("origin")?(a=!0,o[c]=l):r[c]=l}}if(t.transform||(i||n?r.transform=om(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:c="50%",originY:u="50%",originZ:l=0}=o;r.transformOrigin=`${c} ${u} ${l}`}}const Bs=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bc(e,t,n){for(const r in t)!te(t[r])&&!xc(r,n)&&(e[r]=t[r])}function im({transformTemplate:e},t){return f.useMemo(()=>{const n=Bs();return Fs(n,t,e),Object.assign({},n.vars,n.style)},[t])}function am(e,t){const n=e.style||{},r={};return bc(r,n,e),Object.assign(r,im(e,t)),r}function cm(e,t){const n={},r=am(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const lm={offset:"stroke-dashoffset",array:"stroke-dasharray"},um={offset:"strokeDashoffset",array:"strokeDasharray"};function dm(e,t,n=1,r=0,s=!0){e.pathLength=1;const o=s?lm:um;e[o.offset]=k.transform(-r);const i=k.transform(t),a=k.transform(n);e[o.array]=`${i} ${a}`}function wc(e,{attrX:t,attrY:n,attrScale:r,pathLength:s,pathSpacing:o=1,pathOffset:i=0,...a},c,u,l){if(Fs(e,a,u),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:p}=e;h.transform&&(p.transform=h.transform,delete h.transform),(p.transform||h.transformOrigin)&&(p.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),p.transform&&(p.transformBox=l?.transformBox??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),s!==void 0&&dm(h,s,o,i,!1)}const Cc=()=>({...Bs(),attrs:{}}),Sc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function fm(e,t,n,r){const s=f.useMemo(()=>{const o=Cc();return wc(o,t,Sc(r),e.transformTemplate,e.style),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};bc(o,e.style,e),s.style={...o,...s.style}}return s}const hm=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function $s(e){return typeof e!="string"||e.includes("-")?!1:!!(hm.indexOf(e)>-1||/[A-Z]/u.test(e))}function pm(e,t,n,{latestValues:r},s,o=!1){const a=($s(e)?fm:cm)(t,r,s,e),c=Qp(t,typeof e=="string",o),u=e!==f.Fragment?{...c,...a,ref:n}:{},{children:l}=t,h=f.useMemo(()=>te(l)?l.get():l,[l]);return f.createElement(e,{...u,children:h})}function Ho(e){const t=[{},{}];return e?.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Us(e,t,n,r){if(typeof t=="function"){const[s,o]=Ho(r);t=t(n!==void 0?n:e.custom,s,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,o]=Ho(r);t=t(n!==void 0?n:e.custom,s,o)}return t}function yn(e){return te(e)?e.get():e}function mm({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,s){return{latestValues:gm(n,r,s,e),renderState:t()}}function gm(e,t,n,r){const s={},o=r(e,{});for(const p in o)s[p]=yn(o[p]);let{initial:i,animate:a}=e;const c=$n(e),u=yc(e);t&&u&&!c&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let l=n?n.initial===!1:!1;l=l||i===!1;const h=l?a:i;if(h&&typeof h!="boolean"&&!Bn(h)){const p=Array.isArray(h)?h:[h];for(let m=0;m<p.length;m++){const v=Us(e,p[m]);if(v){const{transitionEnd:g,transition:y,...x}=v;for(const b in x){let w=x[b];if(Array.isArray(w)){const S=l?w.length-1:0;w=w[S]}w!==null&&(s[b]=w)}for(const b in g)s[b]=g[b]}}}return s}const Tc=e=>(t,n)=>{const r=f.useContext(Fn),s=f.useContext(Vn),o=()=>mm(e,t,r,s);return n?o():hs(o)};function Ws(e,t,n){const{style:r}=e,s={};for(const o in r)(te(r[o])||t.style&&te(t.style[o])||xc(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(s[o]=r[o]);return s}const vm=Tc({scrapeMotionValuesFromProps:Ws,createRenderState:Bs});function Ac(e,t,n){const r=Ws(e,t,n);for(const s in e)if(te(e[s])||te(t[s])){const o=bt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[o]=e[s]}return r}const ym=Tc({scrapeMotionValuesFromProps:Ac,createRenderState:Cc}),xm=Symbol.for("motionComponentSymbol");function ct(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function bm(e,t,n){return f.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):ct(n)&&(n.current=r))},[t])}const zs=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),wm="framerAppearId",Pc="data-"+zs(wm),Ec=f.createContext({});function Cm(e,t,n,r,s){const{visualElement:o}=f.useContext(Fn),i=f.useContext(gc),a=f.useContext(Vn),c=f.useContext(_s).reducedMotion,u=f.useRef(null);r=r||i.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:c}));const l=u.current,h=f.useContext(Ec);l&&!l.projection&&s&&(l.type==="html"||l.type==="svg")&&Sm(u.current,n,s,h);const p=f.useRef(!1);f.useInsertionEffect(()=>{l&&p.current&&l.update(n,a)});const m=n[Pc],v=f.useRef(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return Sa(()=>{l&&(p.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),v.current&&l.animationState&&l.animationState.animateChanges())}),f.useEffect(()=>{l&&(!v.current&&l.animationState&&l.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),v.current=!1))}),l}function Sm(e,t,n,r){const{layoutId:s,layout:o,drag:i,dragConstraints:a,layoutScroll:c,layoutRoot:u,layoutCrossfade:l}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Rc(e.parent)),e.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:!!i||a&&ct(a),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:r,crossfade:l,layoutScroll:c,layoutRoot:u})}function Rc(e){if(e)return e.options.allowProjection!==!1?e.projection:Rc(e.parent)}function dr(e,{forwardMotionProps:t=!1}={},n,r){n&&qp(n);const s=$s(e)?ym:vm;function o(a,c){let u;const l={...f.useContext(_s),...a,layoutId:Tm(a)},{isStatic:h}=l,p=tm(a),m=s(a,h);if(!h&&ps){Am();const v=Pm(l);u=v.MeasureLayout,p.visualElement=Cm(e,m,l,r,v.ProjectionNode)}return d.jsxs(Fn.Provider,{value:p,children:[u&&p.visualElement?d.jsx(u,{visualElement:p.visualElement,...l}):null,pm(e,a,bm(m,p.visualElement,c),m,h,t)]})}o.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const i=f.forwardRef(o);return i[xm]=e,i}function Tm({layoutId:e}){const t=f.useContext(fs).id;return t&&e!==void 0?t+"-"+e:e}function Am(e,t){f.useContext(gc).strict}function Pm(e){const{drag:t,layout:n}=gt;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function Em(e,t){if(typeof Proxy>"u")return dr;const n=new Map,r=(o,i)=>dr(o,i,e,t),s=(o,i)=>r(o,i);return new Proxy(s,{get:(o,i)=>i==="create"?r:(n.has(i)||n.set(i,dr(i,void 0,e,t)),n.get(i))})}function Mc({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Rm({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Mm(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function fr(e){return e===void 0||e===1}function zr({scale:e,scaleX:t,scaleY:n}){return!fr(e)||!fr(t)||!fr(n)}function Ye(e){return zr(e)||Dc(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Dc(e){return Go(e.x)||Go(e.y)}function Go(e){return e&&e!=="0%"}function Rn(e,t,n){const r=e-n,s=t*r;return n+s}function Yo(e,t,n,r,s){return s!==void 0&&(e=Rn(e,s,r)),Rn(e,n,r)+t}function Kr(e,t=0,n=1,r,s){e.min=Yo(e.min,t,n,r,s),e.max=Yo(e.max,t,n,r,s)}function Nc(e,{x:t,y:n}){Kr(e.x,t.translate,t.scale,t.originPoint),Kr(e.y,n.translate,n.scale,n.originPoint)}const Xo=.999999999999,qo=1.0000000000001;function Dm(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let o,i;for(let a=0;a<s;a++){o=n[a],i=o.projectionDelta;const{visualElement:c}=o.options;c&&c.props.style&&c.props.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ut(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Nc(e,i)),r&&Ye(o.latestValues)&&ut(e,o.latestValues))}t.x<qo&&t.x>Xo&&(t.x=1),t.y<qo&&t.y>Xo&&(t.y=1)}function lt(e,t){e.min=e.min+t,e.max=e.max+t}function Zo(e,t,n,r,s=.5){const o=z(e.min,e.max,s);Kr(e,t,n,o,r)}function ut(e,t){Zo(e.x,t.x,t.scaleX,t.scale,t.originX),Zo(e.y,t.y,t.scaleY,t.scale,t.originY)}function jc(e,t){return Mc(Mm(e.getBoundingClientRect(),t))}function Nm(e,t,n){const r=jc(e,n),{scroll:s}=t;return s&&(lt(r.x,s.offset.x),lt(r.y,s.offset.y)),r}const Jo=()=>({translate:0,scale:1,origin:0,originPoint:0}),dt=()=>({x:Jo(),y:Jo()}),Qo=()=>({min:0,max:0}),Y=()=>({x:Qo(),y:Qo()}),Hr={current:null},Ic={current:!1};function jm(){if(Ic.current=!0,!!ps)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Hr.current=e.matches;e.addEventListener("change",t),t()}else Hr.current=!1}const Im=new WeakMap;function km(e,t,n){for(const r in t){const s=t[r],o=n[r];if(te(s))e.addValue(r,s);else if(te(o))e.addValue(r,mt(s,{owner:e}));else if(o!==s)if(e.hasValue(r)){const i=e.getValue(r);i.liveStyle===!0?i.jump(s):i.hasAnimated||i.set(s)}else{const i=e.getStaticValue(r);e.addValue(r,mt(i!==void 0?i:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const ei=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Om{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:o,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ns,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const p=se.now();this.renderScheduledAt<p&&(this.renderScheduledAt=p,U.render(this.render,!1,!0))};const{latestValues:c,renderState:u}=i;this.latestValues=c,this.baseTarget={...c},this.initialValues=n.initial?{...c}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=$n(n),this.isVariantNode=yc(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const p in h){const m=h[p];c[p]!==void 0&&te(m)&&m.set(c[p])}}mount(t){this.current=t,Im.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Ic.current||jm(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Hr.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Fe(this.notifyUpdate),Fe(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=wt.has(t);r&&this.onBindTransform&&this.onBindTransform();const s=n.on("change",i=>{this.latestValues[t]=i,this.props.onUpdate&&U.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in gt){const n=gt[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Y()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<ei.length;r++){const s=ei[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const o="on"+s,i=t[o];i&&(this.propEventSubscriptions[s]=this.on(s,i))}this.prevMotionValues=km(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=mt(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Ta(r)||Pa(r))?r=parseFloat(r):!Kp(r)&&Be.test(n)&&(r=ac(t,n)),this.setBaseTarget(t,te(r)?r.get():r)),te(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const o=Us(this.props,n,this.presenceContext?.custom);o&&(r=o[t])}if(n&&r!==void 0)return r;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!te(s)?s:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new xs),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}scheduleRenderMicrotask(){ks.render(this.render)}}class kc extends Om{constructor(){super(...arguments),this.KeyframeResolver=Ip}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;te(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Oc(e,{style:t,vars:n},r,s){const o=e.style;let i;for(i in t)o[i]=t[i];s?.applyProjectionStyles(o,r);for(i in n)o.setProperty(i,n[i])}function _m(e){return window.getComputedStyle(e)}class Lm extends kc{constructor(){super(...arguments),this.type="html",this.renderInstance=Oc}readValueFromInstance(t,n){if(wt.has(n))return this.projection?.isProjecting?Lr(n):Jh(t,n);{const r=_m(t),s=(Cs(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return jc(t,n)}build(t,n,r){Fs(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Ws(t,n,r)}}const _c=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Vm(e,t,n,r){Oc(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(_c.has(s)?s:zs(s),t.attrs[s])}class Fm extends kc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Y}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(wt.has(n)){const r=ic(n);return r&&r.default||0}return n=_c.has(n)?n:zs(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Ac(t,n,r)}build(t,n,r){wc(t,n,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,n,r,s){Vm(t,n,r,s)}mount(t){this.isSVGTag=Sc(t.tagName),super.mount(t)}}const Bm=(e,t)=>$s(e)?new Fm(t):new Lm(t,{allowProjection:e!==f.Fragment});function Bt(e,t,n){const r=e.getProps();return Us(r,t,n!==void 0?n:r.custom,e)}const Gr=e=>Array.isArray(e);function $m(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,mt(n))}function Um(e){return Gr(e)?e[e.length-1]||0:e}function Wm(e,t){const n=Bt(e,t);let{transitionEnd:r={},transition:s={},...o}=n||{};o={...o,...r};for(const i in o){const a=Um(o[i]);$m(e,i,a)}}function zm(e){return!!(te(e)&&e.add)}function Yr(e,t){const n=e.getValue("willChange");if(zm(n))return n.add(t);if(!n&&De.WillChange){const r=new De.WillChange("auto");e.addValue("willChange",r),r.add(t)}}function Lc(e){return e.props[Pc]}const Km=e=>e!==null;function Hm(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(Km),o=t&&n!=="loop"&&t%2===1?0:s.length-1;return s[o]}const Gm={type:"spring",stiffness:500,damping:25,restSpeed:10},Ym=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Xm={type:"keyframes",duration:.8},qm={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Zm=(e,{keyframes:t})=>t.length>2?Xm:wt.has(e)?e.startsWith("scale")?Ym(t[1]):Gm:qm;function Jm({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:o,repeatType:i,repeatDelay:a,from:c,elapsed:u,...l}){return!!Object.keys(l).length}const Ks=(e,t,n,r={},s,o)=>i=>{const a=js(r,e)||{},c=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Ce(c);const l={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:p=>{t.set(p),a.onUpdate&&a.onUpdate(p)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:s};Jm(a)||Object.assign(l,Zm(e,l)),l.duration&&(l.duration=Ce(l.duration)),l.repeatDelay&&(l.repeatDelay=Ce(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let h=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(Ur(l),l.delay===0&&(h=!0)),(De.instantAnimations||De.skipAnimations)&&(h=!0,Ur(l),l.delay=0),l.allowFlatten=!a.type&&!a.ease,h&&!o&&t.get()!==void 0){const p=Hm(l.keyframes,a);if(p!==void 0){U.update(()=>{l.onUpdate(p),l.onComplete()});return}}return a.isSync?new Ds(l):new wp(l)};function Qm({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Vc(e,t,{delay:n=0,transitionOverride:r,type:s}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:i,...a}=t;r&&(o=r);const c=[],u=s&&e.animationState&&e.animationState.getState()[s];for(const l in a){const h=e.getValue(l,e.latestValues[l]??null),p=a[l];if(p===void 0||u&&Qm(u,l))continue;const m={delay:n,...js(o||{},l)},v=h.get();if(v!==void 0&&!h.isAnimating&&!Array.isArray(p)&&p===v&&!m.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const x=Lc(e);if(x){const b=window.MotionHandoffAnimation(x,l,U);b!==null&&(m.startTime=b,g=!0)}}Yr(e,l),h.start(Ks(l,h,p,e.shouldReduceMotion&&rc.has(l)?{type:!1}:m,e,g));const y=h.animation;y&&c.push(y)}return i&&Promise.all(c).then(()=>{U.update(()=>{i&&Wm(e,i)})}),c}function Xr(e,t,n={}){const r=Bt(e,t,n.type==="exit"?e.presenceContext?.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(s=n.transitionOverride);const o=r?()=>Promise.all(Vc(e,r,n)):()=>Promise.resolve(),i=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:l,staggerDirection:h}=s;return eg(e,t,c,u,l,h,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[c,u]=a==="beforeChildren"?[o,i]:[i,o];return c().then(()=>u())}else return Promise.all([o(),i(n.delay)])}function eg(e,t,n=0,r=0,s=0,o=1,i){const a=[],c=e.variantChildren.size,u=(c-1)*s,l=typeof r=="function",h=l?p=>r(p,c):o===1?(p=0)=>p*s:(p=0)=>u-p*s;return Array.from(e.variantChildren).sort(tg).forEach((p,m)=>{p.notify("AnimationStart",t),a.push(Xr(p,t,{...i,delay:n+(l?0:r)+h(m)}).then(()=>p.notify("AnimationComplete",t)))}),Promise.all(a)}function tg(e,t){return e.sortNodePosition(t)}function ng(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(o=>Xr(e,o,n));r=Promise.all(s)}else if(typeof t=="string")r=Xr(e,t,n);else{const s=typeof t=="function"?Bt(e,t,n.custom):t;r=Promise.all(Vc(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function Fc(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const rg=Vs.length;function Bc(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Bc(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<rg;n++){const r=Vs[n],s=e.props[r];(Vt(s)||s===!1)&&(t[r]=s)}return t}const sg=[...Ls].reverse(),og=Ls.length;function ig(e){return t=>Promise.all(t.map(({animation:n,options:r})=>ng(e,n,r)))}function ag(e){let t=ig(e),n=ti(),r=!0;const s=c=>(u,l)=>{const h=Bt(e,l,c==="exit"?e.presenceContext?.custom:void 0);if(h){const{transition:p,transitionEnd:m,...v}=h;u={...u,...v,...m}}return u};function o(c){t=c(e)}function i(c){const{props:u}=e,l=Bc(e.parent)||{},h=[],p=new Set;let m={},v=1/0;for(let y=0;y<og;y++){const x=sg[y],b=n[x],w=u[x]!==void 0?u[x]:l[x],S=Vt(w),T=x===c?b.isActive:null;T===!1&&(v=y);let M=w===l[x]&&w!==u[x]&&S;if(M&&r&&e.manuallyAnimateOnMount&&(M=!1),b.protectedKeys={...m},!b.isActive&&T===null||!w&&!b.prevProp||Bn(w)||typeof w=="boolean")continue;const P=cg(b.prevProp,w);let E=P||x===c&&b.isActive&&!M&&S||y>v&&S,R=!1;const N=Array.isArray(w)?w:[w];let O=N.reduce(s(x),{});T===!1&&(O={});const{prevResolvedValues:V={}}=b,B={...V,...O},L=D=>{E=!0,p.has(D)&&(R=!0,p.delete(D)),b.needsAnimating[D]=!0;const C=e.getValue(D);C&&(C.liveStyle=!1)};for(const D in B){const C=O[D],A=V[D];if(m.hasOwnProperty(D))continue;let I=!1;Gr(C)&&Gr(A)?I=!Fc(C,A):I=C!==A,I?C!=null?L(D):p.add(D):C!==void 0&&p.has(D)?L(D):b.protectedKeys[D]=!0}b.prevProp=w,b.prevResolvedValues=O,b.isActive&&(m={...m,...O}),r&&e.blockInitialAnimation&&(E=!1),E&&(!(M&&P)||R)&&h.push(...N.map(D=>({animation:D,options:{type:x}})))}if(p.size){const y={};if(typeof u.initial!="boolean"){const x=Bt(e,Array.isArray(u.initial)?u.initial[0]:u.initial);x&&x.transition&&(y.transition=x.transition)}p.forEach(x=>{const b=e.getBaseTarget(x),w=e.getValue(x);w&&(w.liveStyle=!0),y[x]=b??null}),h.push({animation:y})}let g=!!h.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(h):Promise.resolve()}function a(c,u){if(n[c].isActive===u)return Promise.resolve();e.variantChildren?.forEach(h=>h.animationState?.setActive(c,u)),n[c].isActive=u;const l=i(c);for(const h in n)n[h].protectedKeys={};return l}return{animateChanges:i,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=ti(),r=!0}}}function cg(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Fc(t,e):!1}function Ge(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ti(){return{animate:Ge(!0),whileInView:Ge(),whileHover:Ge(),whileTap:Ge(),whileDrag:Ge(),whileFocus:Ge(),exit:Ge()}}class ze{constructor(t){this.isMounted=!1,this.node=t}update(){}}class lg extends ze{constructor(t){super(t),t.animationState||(t.animationState=ag(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Bn(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ug=0;class dg extends ze{constructor(){super(...arguments),this.id=ug++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const fg={animation:{Feature:lg},exit:{Feature:dg}};function $t(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Zt(e){return{point:{x:e.pageX,y:e.pageY}}}const hg=e=>t=>Os(t)&&e(t,Zt(t));function jt(e,t,n,r){return $t(e,t,hg(n),r)}const $c=1e-4,pg=1-$c,mg=1+$c,Uc=.01,gg=0-Uc,vg=0+Uc;function ne(e){return e.max-e.min}function yg(e,t,n){return Math.abs(e-t)<=n}function ni(e,t,n,r=.5){e.origin=r,e.originPoint=z(t.min,t.max,e.origin),e.scale=ne(n)/ne(t),e.translate=z(n.min,n.max,e.origin)-e.originPoint,(e.scale>=pg&&e.scale<=mg||isNaN(e.scale))&&(e.scale=1),(e.translate>=gg&&e.translate<=vg||isNaN(e.translate))&&(e.translate=0)}function It(e,t,n,r){ni(e.x,t.x,n.x,r?r.originX:void 0),ni(e.y,t.y,n.y,r?r.originY:void 0)}function ri(e,t,n){e.min=n.min+t.min,e.max=e.min+ne(t)}function xg(e,t,n){ri(e.x,t.x,n.x),ri(e.y,t.y,n.y)}function si(e,t,n){e.min=t.min-n.min,e.max=e.min+ne(t)}function kt(e,t,n){si(e.x,t.x,n.x),si(e.y,t.y,n.y)}function ue(e){return[e("x"),e("y")]}const Wc=({current:e})=>e?e.ownerDocument.defaultView:null,oi=(e,t)=>Math.abs(e-t);function bg(e,t){const n=oi(e.x,t.x),r=oi(e.y,t.y);return Math.sqrt(n**2+r**2)}class zc{constructor(t,n,{transformPagePoint:r,contextWindow:s=window,dragSnapToOrigin:o=!1,distanceThreshold:i=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=pr(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,v=bg(p.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!v)return;const{point:g}=p,{timestamp:y}=ee;this.history.push({...g,timestamp:y});const{onStart:x,onMove:b}=this.handlers;m||(x&&x(this.lastMoveEvent,p),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,p)},this.handlePointerMove=(p,m)=>{this.lastMoveEvent=p,this.lastMoveEventInfo=hr(m,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(p,m)=>{this.end();const{onEnd:v,onSessionEnd:g,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=pr(p.type==="pointercancel"?this.lastMoveEventInfo:hr(m,this.transformPagePoint),this.history);this.startEvent&&v&&v(p,x),g&&g(p,x)},!Os(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.distanceThreshold=i,this.contextWindow=s||window;const a=Zt(t),c=hr(a,this.transformPagePoint),{point:u}=c,{timestamp:l}=ee;this.history=[{...u,timestamp:l}];const{onSessionStart:h}=n;h&&h(t,pr(c,this.history)),this.removeListeners=Yt(jt(this.contextWindow,"pointermove",this.handlePointerMove),jt(this.contextWindow,"pointerup",this.handlePointerUp),jt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Fe(this.updatePoint)}}function hr(e,t){return t?{point:t(e.point)}:e}function ii(e,t){return{x:e.x-t.x,y:e.y-t.y}}function pr({point:e},t){return{point:e,delta:ii(e,Kc(t)),offset:ii(e,wg(t)),velocity:Cg(t,.1)}}function wg(e){return e[0]}function Kc(e){return e[e.length-1]}function Cg(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=Kc(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Ce(t)));)n--;if(!r)return{x:0,y:0};const o=Se(s.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const i={x:(s.x-r.x)/o,y:(s.y-r.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function Sg(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?z(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?z(n,e,r.max):Math.min(e,n)),e}function ai(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Tg(e,{top:t,left:n,bottom:r,right:s}){return{x:ai(e.x,n,s),y:ai(e.y,t,r)}}function ci(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Ag(e,t){return{x:ci(e.x,t.x),y:ci(e.y,t.y)}}function Pg(e,t){let n=.5;const r=ne(e),s=ne(t);return s>r?n=Ot(t.min,t.max-r,e.min):r>s&&(n=Ot(e.min,e.max-s,t.min)),Me(0,1,n)}function Eg(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const qr=.35;function Rg(e=qr){return e===!1?e=0:e===!0&&(e=qr),{x:li(e,"left","right"),y:li(e,"top","bottom")}}function li(e,t,n){return{min:ui(e,t),max:ui(e,n)}}function ui(e,t){return typeof e=="number"?e:e[t]||0}const Mg=new WeakMap;class Dg{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Y(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:n=!1,distanceThreshold:r}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:p}=this.getProps();p?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Zt(h).point)},i=(h,p)=>{const{drag:m,dragPropagation:v,onDragStart:g}=this.getProps();if(m&&!v&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Lp(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=p,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ue(x=>{let b=this.getAxisMotionValue(x).get()||0;if(Te.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const S=w.layout.layoutBox[x];S&&(b=ne(S)*(parseFloat(b)/100))}}this.originPoint[x]=b}),g&&U.postRender(()=>g(h,p)),Yr(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},a=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p;const{dragPropagation:m,dragDirectionLock:v,onDirectionLock:g,onDrag:y}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:x}=p;if(v&&this.currentDirection===null){this.currentDirection=Ng(x),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",p.point,x),this.updateAxis("y",p.point,x),this.visualElement.render(),y&&y(h,p)},c=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p,this.stop(h,p),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>ue(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:l}=this.getProps();this.panSession=new zc(t,{onSessionStart:o,onStart:i,onMove:a,onSessionEnd:c,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,distanceThreshold:r,contextWindow:Wc(this.visualElement)})}stop(t,n){const r=t||this.latestPointerEvent,s=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!s||!r)return;const{velocity:i}=s;this.startAnimation(i);const{onDragEnd:a}=this.getProps();a&&U.postRender(()=>a(r,s))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!ln(t,s,this.currentDirection))return;const o=this.getAxisMotionValue(t);let i=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(i=Sg(i,this.constraints[t],this.elastic[t])),o.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&ct(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=Tg(r.layoutBox,t):this.constraints=!1,this.elastic=Rg(n),s!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ue(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Eg(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!ct(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const o=Nm(r,s.root,this.visualElement.getTransformPagePoint());let i=Ag(s.layout.layoutBox,o);if(n){const a=n(Rm(i));this.hasMutatedConstraints=!!a,a&&(i=Mc(a))}return i}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},u=ue(l=>{if(!ln(l,n,this.currentDirection))return;let h=c&&c[l]||{};i&&(h={min:0,max:0});const p=s?200:1e6,m=s?40:1e7,v={type:"inertia",velocity:r?t[l]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(l,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Yr(this.visualElement,t),r.start(Ks(t,r,0,n,this.visualElement,!1))}stopAnimation(){ue(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ue(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ue(n=>{const{drag:r}=this.getProps();if(!ln(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,o=this.getAxisMotionValue(n);if(s&&s.layout){const{min:i,max:a}=s.layout.layoutBox[n];o.set(t[n]-z(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!ct(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};ue(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const c=a.get();s[i]=Pg({min:c,max:c},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ue(i=>{if(!ln(i,t,null))return;const a=this.getAxisMotionValue(i),{min:c,max:u}=this.constraints[i];a.set(z(c,u,s[i]))})}addListeners(){if(!this.visualElement.current)return;Mg.set(this.visualElement,this);const t=this.visualElement.current,n=jt(t,"pointerdown",c=>{const{drag:u,dragListener:l=!0}=this.getProps();u&&l&&this.start(c)}),r=()=>{const{dragConstraints:c}=this.getProps();ct(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,o=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),U.read(r);const i=$t(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(ue(l=>{const h=this.getAxisMotionValue(l);h&&(this.originPoint[l]+=c[l].translate,h.set(h.get()+c[l].translate))}),this.visualElement.render())});return()=>{i(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:i=qr,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function ln(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Ng(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class jg extends ze{constructor(t){super(t),this.removeGroupControls=fe,this.removeListeners=fe,this.controls=new Dg(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||fe}unmount(){this.removeGroupControls(),this.removeListeners()}}const di=e=>(t,n)=>{e&&U.postRender(()=>e(t,n))};class Ig extends ze{constructor(){super(...arguments),this.removePointerDownListener=fe}onPointerDown(t){this.session=new zc(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Wc(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:di(t),onStart:di(n),onMove:r,onEnd:(o,i)=>{delete this.session,s&&U.postRender(()=>s(o,i))}}}mount(){this.removePointerDownListener=jt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const xn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function fi(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Pt={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(k.test(e))e=parseFloat(e);else return e;const n=fi(e,t.target.x),r=fi(e,t.target.y);return`${n}% ${r}%`}},kg={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=Be.parse(e);if(s.length>5)return r;const o=Be.createTransformer(e),i=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,c=n.y.scale*t.y;s[0+i]/=a,s[1+i]/=c;const u=z(a,c,.5);return typeof s[2+i]=="number"&&(s[2+i]/=u),typeof s[3+i]=="number"&&(s[3+i]/=u),o(s)}};let hi=!1;class Og extends f.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:o}=t;nm(_g),o&&(n.group&&n.group.add(o),r&&r.register&&s&&r.register(o),hi&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),xn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:o}=this.props,{projection:i}=r;return i&&(i.isPresent=o,hi=!0,s||t.layoutDependency!==n||n===void 0||t.isPresent!==o?i.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?i.promote():i.relegate()||U.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ks.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Hc(e){const[t,n]=pc(),r=f.useContext(fs);return d.jsx(Og,{...e,layoutGroup:r,switchLayoutGroup:f.useContext(Ec),isPresent:t,safeToRemove:n})}const _g={borderRadius:{...Pt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Pt,borderTopRightRadius:Pt,borderBottomLeftRadius:Pt,borderBottomRightRadius:Pt,boxShadow:kg};function Lg(e,t,n){const r=te(e)?e:mt(e);return r.start(Ks("",r,t,n)),r.animation}const Vg=(e,t)=>e.depth-t.depth;class Fg{constructor(){this.children=[],this.isDirty=!1}add(t){ms(this.children,t),this.isDirty=!0}remove(t){gs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Vg),this.isDirty=!1,this.children.forEach(t)}}function Bg(e,t){const n=se.now(),r=({timestamp:s})=>{const o=s-n;o>=t&&(Fe(r),e(o-t))};return U.setup(r,!0),()=>Fe(r)}const Gc=["TopLeft","TopRight","BottomLeft","BottomRight"],$g=Gc.length,pi=e=>typeof e=="string"?parseFloat(e):e,mi=e=>typeof e=="number"||k.test(e);function Ug(e,t,n,r,s,o){s?(e.opacity=z(0,n.opacity??1,Wg(r)),e.opacityExit=z(t.opacity??1,0,zg(r))):o&&(e.opacity=z(t.opacity??1,n.opacity??1,r));for(let i=0;i<$g;i++){const a=`border${Gc[i]}Radius`;let c=gi(t,a),u=gi(n,a);if(c===void 0&&u===void 0)continue;c||(c=0),u||(u=0),c===0||u===0||mi(c)===mi(u)?(e[a]=Math.max(z(pi(c),pi(u),r),0),(Te.test(u)||Te.test(c))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=z(t.rotate||0,n.rotate||0,r))}function gi(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Wg=Yc(0,.5,ka),zg=Yc(.5,.95,fe);function Yc(e,t,n){return r=>r<e?0:r>t?1:n(Ot(e,t,r))}function vi(e,t){e.min=t.min,e.max=t.max}function le(e,t){vi(e.x,t.x),vi(e.y,t.y)}function yi(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function xi(e,t,n,r,s){return e-=t,e=Rn(e,1/n,r),s!==void 0&&(e=Rn(e,1/s,r)),e}function Kg(e,t=0,n=1,r=.5,s,o=e,i=e){if(Te.test(t)&&(t=parseFloat(t),t=z(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=z(o.min,o.max,r);e===o&&(a-=t),e.min=xi(e.min,t,n,a,s),e.max=xi(e.max,t,n,a,s)}function bi(e,t,[n,r,s],o,i){Kg(e,t[n],t[r],t[s],t.scale,o,i)}const Hg=["x","scaleX","originX"],Gg=["y","scaleY","originY"];function wi(e,t,n,r){bi(e.x,t,Hg,n?n.x:void 0,r?r.x:void 0),bi(e.y,t,Gg,n?n.y:void 0,r?r.y:void 0)}function Ci(e){return e.translate===0&&e.scale===1}function Xc(e){return Ci(e.x)&&Ci(e.y)}function Si(e,t){return e.min===t.min&&e.max===t.max}function Yg(e,t){return Si(e.x,t.x)&&Si(e.y,t.y)}function Ti(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function qc(e,t){return Ti(e.x,t.x)&&Ti(e.y,t.y)}function Ai(e){return ne(e.x)/ne(e.y)}function Pi(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Xg{constructor(){this.members=[]}add(t){ms(this.members,t),t.scheduleRender()}remove(t){if(gs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const o=this.members[s];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function qg(e,t,n){let r="";const s=e.x.translate/t.x,o=e.y.translate/t.y,i=n?.z||0;if((s||o||i)&&(r=`translate3d(${s}px, ${o}px, ${i}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:l,rotateX:h,rotateY:p,skewX:m,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),l&&(r+=`rotate(${l}deg) `),h&&(r+=`rotateX(${h}deg) `),p&&(r+=`rotateY(${p}deg) `),m&&(r+=`skewX(${m}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,c=e.y.scale*t.y;return(a!==1||c!==1)&&(r+=`scale(${a}, ${c})`),r||"none"}const mr=["","X","Y","Z"],Zg=1e3;let Jg=0;function gr(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Zc(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Lc(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",U,!(s||o))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Zc(r)}function Jc({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(i={},a=t?.()){this.id=Jg++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(tv),this.nodes.forEach(ov),this.nodes.forEach(iv),this.nodes.forEach(nv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Fg)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new xs),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const c=this.eventHandlers.get(i);c&&c.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i){if(this.instance)return;this.isSVG=hc(i)&&!Wp(i),this.instance=i;const{layoutId:a,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(c||a)&&(this.isLayoutDirty=!0),e){let l,h=0;const p=()=>this.root.updateBlockedByResize=!1;U.read(()=>{h=window.innerWidth}),e(i,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,l&&l(),l=Bg(p,250),xn.hasAnimatedSinceResize&&(xn.hasAnimatedSinceResize=!1,this.nodes.forEach(Mi)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||c)&&this.addEventListener("didUpdate",({delta:l,hasLayoutChanged:h,hasRelativeLayoutChanged:p,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||u.getDefaultTransition()||dv,{onLayoutAnimationStart:g,onLayoutAnimationComplete:y}=u.getProps(),x=!this.targetLayout||!qc(this.targetLayout,m),b=!h&&p;if(this.options.layoutRoot||this.resumeFrom||b||h&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const w={...js(v,"layout"),onPlay:g,onComplete:y};(u.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w),this.setAnimationOrigin(l,b)}else h||Mi(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Fe(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(av),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Zc(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){const h=this.path[l];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ei);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Ri);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(sv),this.nodes.forEach(Qg),this.nodes.forEach(ev)):this.nodes.forEach(Ri),this.clearAllSnapshots();const a=se.now();ee.delta=Me(0,1e3/60,a-ee.timestamp),ee.timestamp=a,ee.isProcessing=!0,or.update.process(ee),or.preRender.process(ee),or.render.process(ee),ee.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ks.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rv),this.sharedNodes.forEach(cv)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ne(this.snapshot.measuredBox.x)&&!ne(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Y(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&this.instance){const c=r(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:c,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!s)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Xc(this.projectionDelta),c=this.getTransformTemplate(),u=c?c(this.latestValues,""):void 0,l=u!==this.prevTransformTemplateValue;i&&this.instance&&(a||Ye(this.latestValues)||l)&&(s(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return i&&(c=this.removeTransform(c)),fv(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return Y();const a=i.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(hv))){const{scroll:u}=this.root;u&&(lt(a.x,u.offset.x),lt(a.y,u.offset.y))}return a}removeElementScroll(i){const a=Y();if(le(a,i),this.scroll?.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:l,options:h}=u;u!==this.root&&l&&h.layoutScroll&&(l.wasRoot&&le(a,i),lt(a.x,l.offset.x),lt(a.y,l.offset.y))}return a}applyTransform(i,a=!1){const c=Y();le(c,i);for(let u=0;u<this.path.length;u++){const l=this.path[u];!a&&l.options.layoutScroll&&l.scroll&&l!==l.root&&ut(c,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),Ye(l.latestValues)&&ut(c,l.latestValues)}return Ye(this.latestValues)&&ut(c,this.latestValues),c}removeTransform(i){const a=Y();le(a,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!Ye(u.latestValues))continue;zr(u.latestValues)&&u.updateSnapshot();const l=Y(),h=u.measurePageBox();le(l,h),wi(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,l)}return Ye(this.latestValues)&&wi(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ee.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:l,layoutId:h}=this.options;if(!(!this.layout||!(l||h))){if(this.resolvedRelativeTargetAt=ee.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Y(),this.relativeTargetOrigin=Y(),kt(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Y(),this.targetWithTransforms=Y()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xg(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):le(this.target,this.layout.layoutBox),Nc(this.target,this.targetDelta)):le(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Y(),this.relativeTargetOrigin=Y(),kt(this.relativeTargetOrigin,this.target,p.target),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||zr(this.parent.latestValues)||Dc(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const i=this.getLead(),a=!!this.resumingFrom||this!==i;let c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===ee.timestamp&&(c=!1),c)return;const{layout:u,layoutId:l}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||l))return;le(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,p=this.treeScale.y;Dm(this.layoutCorrected,this.treeScale,this.path,a),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=Y());const{target:m}=i;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(yi(this.prevProjectionDelta.x,this.projectionDelta.x),yi(this.prevProjectionDelta.y,this.projectionDelta.y)),It(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==p||!Pi(this.projectionDelta.x,this.prevProjectionDelta.x)||!Pi(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.visualElement?.scheduleRender(),i){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=dt(),this.projectionDelta=dt(),this.projectionDeltaWithTransform=dt()}setAnimationOrigin(i,a=!1){const c=this.snapshot,u=c?c.latestValues:{},l={...this.latestValues},h=dt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=Y(),m=c?c.source:void 0,v=this.layout?this.layout.source:void 0,g=m!==v,y=this.getStack(),x=!y||y.members.length<=1,b=!!(g&&!x&&this.options.crossfade===!0&&!this.path.some(uv));this.animationProgress=0;let w;this.mixTargetDelta=S=>{const T=S/1e3;Di(h.x,i.x,T),Di(h.y,i.y,T),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(kt(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),lv(this.relativeTarget,this.relativeTargetOrigin,p,T),w&&Yg(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=Y()),le(w,this.relativeTarget)),g&&(this.animationValues=l,Ug(l,u,this.latestValues,T,b,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Fe(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{xn.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=mt(0)),this.currentAnimation=Lg(this.motionValue,[0,1e3],{...i,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onStop:()=>{},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Zg),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:c,layout:u,latestValues:l}=i;if(!(!a||!c||!u)){if(this!==i&&this.layout&&u&&Qc(this.options.animationType,this.layout.layoutBox,u.layoutBox)){c=this.target||Y();const h=ne(this.layout.layoutBox.x);c.x.min=i.target.x.min,c.x.max=c.x.min+h;const p=ne(this.layout.layoutBox.y);c.y.min=i.target.y.min,c.y.max=c.y.min+p}le(a,c),ut(a,l),It(this.projectionDeltaWithTransform,this.layoutCorrected,a,l)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Xg),this.sharedNodes.get(i).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){const{layoutId:i}=this.options;return i?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:i}=this.options;return i?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:c}={}){const u=this.getStack();u&&u.promote(this,c),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:c}=i;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(a=!0),!a)return;const u={};c.z&&gr("z",i,u,this.animationValues);for(let l=0;l<mr.length;l++)gr(`rotate${mr[l]}`,i,u,this.animationValues),gr(`skew${mr[l]}`,i,u,this.animationValues);i.render();for(const l in u)i.setStaticValue(l,u[l]),this.animationValues&&(this.animationValues[l]=u[l]);i.scheduleRender()}applyProjectionStyles(i,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){i.visibility="hidden";return}const c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,i.visibility="",i.opacity="",i.pointerEvents=yn(a?.pointerEvents)||"",i.transform=c?c(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(i.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,i.pointerEvents=yn(a?.pointerEvents)||""),this.hasProjected&&!Ye(this.latestValues)&&(i.transform=c?c({},""):"none",this.hasProjected=!1);return}i.visibility="";const l=u.animationValues||u.latestValues;this.applyTransformsToTarget();let h=qg(this.projectionDeltaWithTransform,this.treeScale,l);c&&(h=c(l,h)),i.transform=h;const{x:p,y:m}=this.projectionDelta;i.transformOrigin=`${p.origin*100}% ${m.origin*100}% 0`,u.animationValues?i.opacity=u===this?l.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:l.opacityExit:i.opacity=u===this?l.opacity!==void 0?l.opacity:"":l.opacityExit!==void 0?l.opacityExit:0;for(const v in Ft){if(l[v]===void 0)continue;const{correct:g,applyTo:y,isCSSVariable:x}=Ft[v],b=h==="none"?l[v]:g(l[v],u);if(y){const w=y.length;for(let S=0;S<w;S++)i[y[S]]=b}else x?this.options.visualElement.renderState.vars[v]=b:i[v]=b}this.options.layoutId&&(i.pointerEvents=u===this?yn(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>i.currentAnimation?.stop()),this.root.nodes.forEach(Ei),this.root.sharedNodes.clear()}}}function Qg(e){e.updateLayout()}function ev(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=e.layout,{animationType:s}=e.options,o=t.source!==e.layout.source;s==="size"?ue(l=>{const h=o?t.measuredBox[l]:t.layoutBox[l],p=ne(h);h.min=n[l].min,h.max=h.min+p}):Qc(s,t.layoutBox,n)&&ue(l=>{const h=o?t.measuredBox[l]:t.layoutBox[l],p=ne(n[l]);h.max=h.min+p,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[l].max=e.relativeTarget[l].min+p)});const i=dt();It(i,n,t.layoutBox);const a=dt();o?It(a,e.applyTransform(r,!0),t.measuredBox):It(a,n,t.layoutBox);const c=!Xc(i);let u=!1;if(!e.resumeFrom){const l=e.getClosestProjectingParent();if(l&&!l.resumeFrom){const{snapshot:h,layout:p}=l;if(h&&p){const m=Y();kt(m,t.layoutBox,h.layoutBox);const v=Y();kt(v,n,p.layoutBox),qc(m,v)||(u=!0),l.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=m,e.relativeParent=l)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:i,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function tv(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rv(e){e.clearSnapshot()}function Ei(e){e.clearMeasurements()}function Ri(e){e.isLayoutDirty=!1}function sv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Mi(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ov(e){e.resolveTargetDelta()}function iv(e){e.calcProjection()}function av(e){e.resetSkewAndRotation()}function cv(e){e.removeLeadSnapshot()}function Di(e,t,n){e.translate=z(t.translate,0,n),e.scale=z(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Ni(e,t,n,r){e.min=z(t.min,n.min,r),e.max=z(t.max,n.max,r)}function lv(e,t,n,r){Ni(e.x,t.x,n.x,r),Ni(e.y,t.y,n.y,r)}function uv(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const dv={duration:.45,ease:[.4,0,.1,1]},ji=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Ii=ji("applewebkit/")&&!ji("chrome/")?Math.round:fe;function ki(e){e.min=Ii(e.min),e.max=Ii(e.max)}function fv(e){ki(e.x),ki(e.y)}function Qc(e,t,n){return e==="position"||e==="preserve-aspect"&&!yg(Ai(t),Ai(n),.2)}function hv(e){return e!==e.root&&e.scroll?.wasRoot}const pv=Jc({attachResizeListener:(e,t)=>$t(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),vr={current:void 0},el=Jc({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!vr.current){const e=new pv({});e.mount(window),e.setOptions({layoutScroll:!0}),vr.current=e}return vr.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),mv={pan:{Feature:Ig},drag:{Feature:jg,ProjectionNode:el,MeasureLayout:Hc}};function Oi(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,o=r[s];o&&U.postRender(()=>o(t,Zt(t)))}class gv extends ze{mount(){const{current:t}=this.node;t&&(this.unmount=Vp(t,(n,r)=>(Oi(this.node,r,"Start"),s=>Oi(this.node,s,"End"))))}unmount(){}}class vv extends ze{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Yt($t(this.node.current,"focus",()=>this.onFocus()),$t(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function _i(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),o=r[s];o&&U.postRender(()=>o(t,Zt(t)))}class yv extends ze{mount(){const{current:t}=this.node;t&&(this.unmount=Up(t,(n,r)=>(_i(this.node,r,"Start"),(s,{success:o})=>_i(this.node,s,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Zr=new WeakMap,yr=new WeakMap,xv=e=>{const t=Zr.get(e.target);t&&t(e)},bv=e=>{e.forEach(xv)};function wv({root:e,...t}){const n=e||document;yr.has(n)||yr.set(n,{});const r=yr.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(bv,{root:e,...t})),r[s]}function Cv(e,t,n){const r=wv(t);return Zr.set(e,n),r.observe(e),()=>{Zr.delete(e),r.unobserve(e)}}const Sv={some:0,all:1};class Tv extends ze{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:o}=t,i={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:Sv[s]},a=c=>{const{isIntersecting:u}=c;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:l,onViewportLeave:h}=this.node.getProps(),p=u?l:h;p&&p(c)};return Cv(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Av(t,n))&&this.startObserver()}unmount(){}}function Av({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Pv={inView:{Feature:Tv},tap:{Feature:yv},focus:{Feature:vv},hover:{Feature:gv}},Ev={layout:{ProjectionNode:el,MeasureLayout:Hc}},Rv={...fg,...Pv,...mv,...Ev},Hs=Em(Rv,Bm),Mv=(...e)=>e.filter(Boolean).join(" "),Mn=(...e)=>sf(Mv(e)),Dv=us("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-black text-white hover:bg-gray-800",destructive:"border border-black text-black hover:bg-gray-100",outline:"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",secondary:"bg-gray-200 text-black hover:bg-gray-300",ghost:"text-black hover:bg-gray-100 hover:text-black",link:"text-black underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default"}}),Jt=ye.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},o)=>d.jsx("button",{className:Mn(Dv({variant:t,size:n,className:e})),ref:o,...s}));Jt.displayName="Button";const tl=ye.forwardRef(({className:e,...t},n)=>d.jsx("textarea",{className:Mn("flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black",e),ref:n,...t}));tl.displayName="Textarea";const Nv=({size:e=16})=>d.jsx("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:d.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})}),jv=({size:e=16})=>d.jsx("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:d.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})});function Iv({onSelectAction:e}){const t=[{title:"受限空间内气体环境满足作业要求下",label:"气体检测分析合格标准是多少",action:"受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少"},{title:"受限空间作业前,应根据受限空间",label:"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",action:"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准"},{title:"固定式配电箱及开关箱的底面",label:"离地面垂直高度的高度是多少",action:"固定式配电箱及开关箱的底面离地面垂直高度的高度是多少"},{title:"八大保命原则",label:"是什么",action:"中化集团八大保命原则"}];return d.jsx("div",{"data-testid":"suggested-actions",className:"grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full",children:d.jsx(mc,{children:t.map((n,r)=>d.jsx(Hs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*r},className:"block",children:d.jsxs(Jt,{variant:"ghost",onClick:()=>e(n.action),className:`text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900`,children:[d.jsx("span",{className:"font-medium",children:n.title}),d.jsx("span",{className:"text-gray-500",children:n.label})]})},`suggested-action-${r}`))})})}const kv=f.memo(Iv,(e,t)=>!(e.chatId!==t.chatId||e.selectedVisibilityType!==t.selectedVisibilityType)),Li=({attachment:e,isUploading:t=!1})=>{const{name:n,url:r,contentType:s}=e;return d.jsxs("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-1",children:[d.jsxs("div",{className:"w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300",children:[s?.startsWith("image/")&&r?d.jsx("img",{src:r,alt:n??"An image attachment",className:"rounded-md size-full object-cover grayscale"},r):d.jsxs("div",{className:"flex items-center justify-center text-xs text-gray-600 text-center p-1",children:["File: ",n?.split(".").pop()?.toUpperCase()||"Unknown"]}),t&&d.jsx("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-gray-500",children:d.jsx(Dr,{className:"size-5"})})]}),d.jsx("div",{className:"text-xs text-gray-600 max-w-20 truncate",children:n})]})};function Ov({onStop:e}){return d.jsx(Jt,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border border-black text-white",onClick:t=>{t.preventDefault(),e()},"aria-label":"Stop generating",children:d.jsx(Nv,{size:14})})}const _v=f.memo(Ov,(e,t)=>e.onStop===t.onStop);function Lv({submitForm:e,input:t,uploadQueue:n,attachments:r,canSend:s,isGenerating:o}){const i=n.length>0||!s||o||t.trim().length===0&&r.length===0;return d.jsx(Jt,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit",onClick:a=>{a.preventDefault(),i||e()},disabled:i,"aria-label":"Send message",children:d.jsx(jv,{size:14})})}const Vv=f.memo(Lv,(e,t)=>!(e.input!==t.input||e.uploadQueue.length!==t.uploadQueue.length||e.attachments.length!==t.attachments.length||e.attachments.length>0&&!Xf(e.attachments,t.attachments)||e.canSend!==t.canSend||e.isGenerating!==t.isGenerating));function Fv({chatId:e,messages:t,attachments:n,setAttachments:r,onSendMessage:s,onStopGenerating:o,isGenerating:i,canSend:a,className:c,selectedVisibilityType:u}){const l=f.useRef(null),h=f.useRef(null),[p,m]=f.useState(""),[v,g]=f.useState([]),y=()=>{const R=l.current;R&&(R.style.height="auto",R.style.height=`${R.scrollHeight+2}px`)},x=f.useCallback(()=>{const R=l.current;R&&(R.style.height="auto",R.rows=1,y())},[]);f.useEffect(()=>{l.current&&y()},[p]);const b=R=>{m(R.target.value)},w=async R=>(console.log(`MOCK: Simulating upload for file: ${R.name}`),new Promise(N=>{setTimeout(()=>{try{const V={url:URL.createObjectURL(R),name:R.name,contentType:R.type||"application/octet-stream",size:R.size};console.log(`MOCK: Upload successful for ${R.name}`),N(V)}catch(O){console.error("MOCK: Failed to create object URL for preview:",O),N(void 0)}finally{g(O=>O.filter(V=>V!==R.name))}},700)})),S=f.useCallback(async R=>{const N=Array.from(R.target.files||[]);if(N.length===0)return;g(D=>[...D,...N.map(C=>C.name)]),h.current&&(h.current.value="");const O=25*1024*1024,V=N.filter(D=>D.size<=O),B=N.filter(D=>D.size>O);B.length>0&&(console.warn(`Skipped ${B.length} files larger than ${O/1024/1024}MB.`),g(D=>D.filter(C=>!B.some(A=>A.name===C))));const L=V.map(D=>w(D)),F=(await Promise.all(L)).filter(D=>D!==void 0);r(D=>[...D,...F])},[r,w]),T=f.useCallback(R=>{R.url.startsWith("blob:")&&URL.revokeObjectURL(R.url),r(N=>N.filter(O=>O.url!==R.url||O.name!==R.name)),l.current?.focus()},[r,l]),M=f.useCallback(()=>{if(p.trim().length===0&&n.length===0){console.warn("Please enter a message or add an attachment.");return}s({input:p,attachments:n}),m(""),r([]),n.forEach(R=>{R.url.startsWith("blob:")&&URL.revokeObjectURL(R.url)}),x(),l.current?.focus()},[p,n,s,r,l,x]),P=t.length===0&&n.length===0&&v.length===0,E=i||v.length>0;return d.jsxs("div",{className:Mn("relative w-full flex flex-col gap-4",c),children:[d.jsx(mc,{children:P&&d.jsx(Hs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.2},children:d.jsx(kv,{onSelectAction:R=>{m(R),requestAnimationFrame(()=>{y(),l.current?.focus()})},chatId:e,selectedVisibilityType:u})},"suggested-actions-container")}),d.jsx("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:h,multiple:!0,onChange:S,tabIndex:-1,disabled:E,accept:"image/*,video/*,audio/*,.pdf"}),(n.length>0||v.length>0)&&d.jsxs("div",{"data-testid":"attachments-preview",className:"flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1",children:[n.map(R=>d.jsxs("div",{className:"relative group",children:[d.jsx(Li,{attachment:R,isUploading:!1}),d.jsx(Jt,{variant:"destructive",size:"icon",className:"absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>T(R),"aria-label":`Remove ${R.name}`,children:d.jsx(Ln,{className:"size-3"})})]},R.url||R.name)),v.map((R,N)=>d.jsx(Li,{attachment:{url:"",name:R,contentType:"",size:0},isUploading:!0},`upload-${R}-${N}`))]}),d.jsx(tl,{"data-testid":"multimodal-input",ref:l,placeholder:"你想问什么",value:p,onChange:b,className:Mn("min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10","bg-gray-100 border border-gray-300",c),style:{color:"black"},rows:1,autoFocus:!0,disabled:!a||i||v.length>0,onKeyDown:R=>{R.key==="Enter"&&!R.shiftKey&&!R.nativeEvent.isComposing&&(R.preventDefault(),a&&!i&&v.length===0&&(p.trim().length>0||n.length>0)&&M())}}),d.jsx("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10",children:i?d.jsx(_v,{onStop:o}):d.jsx(Vv,{submitForm:M,input:p,uploadQueue:v,attachments:n,canSend:a,isGenerating:i})})]})}function Bv({onSend:e,showSuggestedActions:t=!0}={}){const[n,r]=f.useState([]),[s,o]=f.useState(!1),[i]=f.useState("demo-input-only"),[a,c]=f.useState(t),u=f.useCallback(({input:m,attachments:v})=>{console.log("--- 发送消息 ---"),console.log("输入:",m),console.log("附件:",v),console.log("---------------------------------"),e&&m.trim()&&e(m.trim()),o(!0),setTimeout(()=>{o(!1),r([])},500)},[e]),l=f.useCallback(()=>{console.log("停止按钮被点击（模拟）。"),o(!1)},[]);return d.jsx("div",{className:"w-full max-w-3xl mx-auto p-4",children:d.jsxs("div",{className:"flex flex-col gap-4",children:[t&&d.jsx("div",{className:"flex justify-center",children:d.jsx("button",{onClick:()=>c(!a),className:"p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm",title:a?"收起快捷输入":"展开快捷输入",children:a?d.jsx(xf,{className:"w-4 h-4 text-gray-600"}):d.jsx(wa,{className:"w-4 h-4 text-gray-600"})})}),d.jsx("div",{children:d.jsx(Fv,{chatId:i,messages:t&&a?[]:[{id:"dummy",content:"",role:"user"}],attachments:n,setAttachments:r,onSendMessage:u,onStopGenerating:l,isGenerating:s,canSend:!0,selectedVisibilityType:"private"})})]})})}const Un=f.forwardRef(({className:e,type:t,...n},r)=>d.jsx("input",{type:t,className:j("flex h-9 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm text-foreground shadow-sm shadow-black/5 transition-shadow placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",t==="search"&&"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",t==="file"&&"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground",e),ref:r,...n}));Un.displayName="Input";function $v({onUpload:e}={}){const t=f.useRef(null),n=f.useRef(null),[r,s]=f.useState(null),[o,i]=f.useState(null),a=f.useCallback(()=>{n.current?.click()},[]),c=f.useCallback(l=>{const h=l.target.files?.[0];if(h){i(h.name);const p=URL.createObjectURL(h);s(p),t.current=p,e?.(p)}},[e]),u=f.useCallback(()=>{r&&URL.revokeObjectURL(r),s(null),i(null),t.current=null,n.current&&(n.current.value="")},[r]);return f.useEffect(()=>()=>{t.current&&URL.revokeObjectURL(t.current)},[]),{previewUrl:r,fileName:o,fileInputRef:n,handleThumbnailClick:a,handleFileChange:c,handleRemove:u}}function Uv({onFileSelect:e,fileContent:t}){const{fileName:n,fileInputRef:r,handleThumbnailClick:s,handleFileChange:o,handleRemove:i}=$v({onUpload:g=>console.log("Uploaded image URL:",g)}),[a,c]=f.useState(!1),u=f.useCallback(g=>{o(g);const y=g.target.files?.[0]||null;e?.(y)},[o,e]),l=f.useCallback(()=>{i(),e?.(null)},[i,e]),h=g=>{g.preventDefault(),g.stopPropagation()},p=g=>{g.preventDefault(),g.stopPropagation(),c(!0)},m=g=>{g.preventDefault(),g.stopPropagation(),c(!1)},v=f.useCallback(g=>{g.preventDefault(),g.stopPropagation(),c(!1);const y=g.dataTransfer.files?.[0];if(y){if(r.current){const x=new DataTransfer;x.items.add(y),r.current.files=x.files;const b=new Event("change",{bubbles:!0});r.current.dispatchEvent(b)}e?.(y)}},[r,e]);return d.jsxs("div",{className:"w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm",children:[d.jsxs("div",{className:"space-y-2",children:[d.jsx("h3",{className:"text-lg font-medium",children:"文件上传"}),d.jsx("p",{className:"text-sm text-muted-foreground",children:"支持格式: TXT, PDF, DOCX"})]}),d.jsx(Un,{type:"file",accept:".txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document",className:"hidden",ref:r,onChange:u}),n?d.jsxs("div",{className:"relative",children:[d.jsx("div",{className:"relative h-64 overflow-hidden rounded-lg border bg-background",children:d.jsx("div",{className:"h-full w-full p-4 overflow-auto",children:t?d.jsx("div",{className:"text-sm text-foreground whitespace-pre-wrap font-mono leading-relaxed",children:t}):d.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:d.jsxs("div",{className:"text-center",children:[d.jsx("p",{className:"text-sm font-medium",children:"Preview"}),d.jsx("p",{className:"text-xs",children:"文件内容将在这里显示"})]})})})}),n&&d.jsxs("div",{className:"mt-2 flex items-center gap-2 text-sm text-muted-foreground",children:[d.jsx("span",{className:"truncate",children:n}),d.jsx("button",{onClick:l,className:"ml-auto rounded-full p-1 hover:bg-muted",children:d.jsx(Ln,{className:"h-4 w-4"})})]})]}):d.jsxs("div",{onClick:s,onDragOver:h,onDragEnter:p,onDragLeave:m,onDrop:v,className:j("flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",a&&"border-primary/50 bg-primary/5"),children:[d.jsx("div",{className:"rounded-full bg-background p-3 shadow-sm",children:d.jsx(Ef,{className:"h-6 w-6 text-muted-foreground"})}),d.jsxs("div",{className:"text-center",children:[d.jsx("p",{className:"text-sm font-medium",children:"点击选择文件"}),d.jsx("p",{className:"text-xs text-muted-foreground",children:"或拖拽文件到此处"})]})]})]})}function Wv({text:e,selected:t,setSelected:n,discount:r=!1}){return d.jsxs("button",{onClick:()=>n(e),className:j("relative w-fit px-4 py-2 text-sm font-semibold capitalize","text-foreground transition-colors",r&&"flex items-center justify-center gap-2.5"),children:[d.jsx("span",{className:"relative z-10",children:e}),t&&d.jsx(Hs.span,{layoutId:"tab",transition:{type:"spring",duration:.4},className:"absolute inset-0 z-0 rounded-full bg-background shadow-sm"})]})}async function zv(e,t){const n={id:t.id,userId:e,title:t.title};return(await de.insert(Z).values(n).returning())[0]}async function Kv(e){return await de.select().from(Z).where(ae(Z.userId,e)).orderBy(uf(Z.updatedAt))}async function Hv(e,t){return(await de.select().from(Z).where(ds(ae(Z.id,e),ae(Z.userId,t))).limit(1))[0]||null}async function Gv(e,t,n){return(await de.update(Z).set({title:n,updatedAt:new Date().toISOString()}).where(ds(ae(Z.id,e),ae(Z.userId,t))).returning()).length>0}async function Yv(e,t){return await de.delete(Le).where(ae(Le.conversationId,e)),(await de.delete(Z).where(ds(ae(Z.id,e),ae(Z.userId,t))).returning()).length>0}async function Xv(e){const t={id:e.id,conversationId:e.conversationId,content:e.content,role:e.role,docReferences:e.docReferences?JSON.stringify(e.docReferences):null};return await de.update(Z).set({updatedAt:new Date().toISOString()}).where(ae(Z.id,e.conversationId)),(await de.insert(Le).values(t).returning())[0]}async function qv(e){return(await de.select().from(Le).where(ae(Le.conversationId,e)).orderBy(Le.timestamp)).map(n=>({...n,docReferences:n.docReferences?JSON.parse(n.docReferences):void 0}))}async function Vi(e,t){const n=await Hv(e,t);if(!n)return null;const r=await qv(e);return{conversation:n,messages:r}}async function Zv(e){const t=await de.select({id:Z.id}).from(Z).where(ae(Z.userId,e));for(const n of t)await de.delete(Le).where(ae(Le.conversationId,n.id));await de.delete(Z).where(ae(Z.userId,e))}const nl=cf()(lf((e,t)=>({conversations:[],currentConversationId:null,isLoading:!1,error:null,createConversation:async(n="新对话")=>{const s=xe.getState().user;if(!s)throw new Error("用户未登录");e({isLoading:!0,error:null});try{const o=`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await zv(s.id,{id:o,title:n});const i={id:o,title:n,messages:[],createdAt:new Date,updatedAt:new Date};return e(a=>({conversations:[i,...a.conversations],currentConversationId:o,isLoading:!1})),o}catch(o){const i=o instanceof Error?o.message:"创建对话失败";throw e({error:i,isLoading:!1}),o}},deleteConversation:async n=>{const s=xe.getState().user;if(!s)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await Yv(n,s.id),e(o=>{const i=o.conversations.filter(c=>c.id!==n),a=o.currentConversationId===n?i.length>0?i[0].id:null:o.currentConversationId;return{conversations:i,currentConversationId:a,isLoading:!1}})}catch(o){const i=o instanceof Error?o.message:"删除对话失败";throw e({error:i,isLoading:!1}),o}},updateConversationTitle:async(n,r)=>{const o=xe.getState().user;if(!o)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await Gv(n,o.id,r),e(i=>({conversations:i.conversations.map(a=>a.id===n?{...a,title:r,updatedAt:new Date}:a),isLoading:!1}))}catch(i){const a=i instanceof Error?i.message:"更新标题失败";throw e({error:a,isLoading:!1}),i}},setCurrentConversation:n=>{e({currentConversationId:n})},addMessage:async(n,r,s,o)=>{e({isLoading:!0,error:null});try{const i=`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await Xv({id:i,conversationId:n,content:r,role:s,docReferences:o});const a={id:i,content:r,role:s,timestamp:new Date,docReferences:o};e(c=>({conversations:c.conversations.map(u=>u.id===n?{...u,messages:[...u.messages,a],updatedAt:new Date}:u),isLoading:!1}))}catch(i){const a=i instanceof Error?i.message:"添加消息失败";throw e({error:a,isLoading:!1}),i}},getCurrentConversation:()=>{const n=t();return n.conversations.find(r=>r.id===n.currentConversationId)||null},loadUserConversations:async()=>{const r=xe.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{const s=await Kv(r.id),o=await Promise.all(s.map(async i=>{const a=await Vi(i.id,r.id);return{id:i.id,title:i.title,messages:a?.messages.map(c=>({id:c.id,content:c.content,role:c.role,timestamp:new Date(c.timestamp)}))||[],createdAt:new Date(i.createdAt),updatedAt:new Date(i.updatedAt)}}));e({conversations:o,isLoading:!1,currentConversationId:t().currentConversationId&&o.find(i=>i.id===t().currentConversationId)?t().currentConversationId:null})}catch(s){const o=s instanceof Error?s.message:"加载对话失败";e({error:o,isLoading:!1})}},syncConversationWithDB:async n=>{const s=xe.getState().user;if(s)try{const o=await Vi(n,s.id);if(!o)return;const i={id:o.conversation.id,title:o.conversation.title,messages:o.messages.map(a=>({id:a.id,content:a.content,role:a.role,timestamp:new Date(a.timestamp)})),createdAt:new Date(o.conversation.createdAt),updatedAt:new Date(o.conversation.updatedAt)};e(a=>({conversations:a.conversations.map(c=>c.id===n?i:c)}))}catch(o){console.error("同步对话失败:",o)}},clearAllConversations:async()=>{const r=xe.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{await Zv(r.id),e({conversations:[],currentConversationId:null,isLoading:!1})}catch(s){const o=s instanceof Error?s.message:"清空对话失败";throw e({error:o,isLoading:!1}),s}},setLoading:n=>{e({isLoading:n})},setError:n=>{e({error:n})}}),{name:"conversation-store",partialize:e=>({currentConversationId:e.currentConversationId})})),xr=768;function Jv(){const[e,t]=f.useState(void 0);return f.useEffect(()=>{const n=window.matchMedia(`(max-width: ${xr-1}px)`),r=()=>{t(window.innerWidth<xr)};return n.addEventListener("change",r),t(window.innerWidth<xr),()=>n.removeEventListener("change",r)},[]),!!e}function _(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e?.(s),n===!1||!s.defaultPrevented)return t?.(s)}}var Qv=ua[" useId ".trim().toString()]||(()=>{}),ey=0;function Qe(e){const[t,n]=f.useState(Qv());return Ve(()=>{n(r=>r??String(ey++))},[e]),e||(t?`radix-${t}`:"")}var ty=ua[" useInsertionEffect ".trim().toString()]||Ve;function Wn({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,o,i]=ny({defaultProp:t,onChange:n}),a=e!==void 0,c=a?e:s;{const l=f.useRef(e!==void 0);f.useEffect(()=>{const h=l.current;h!==a&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),l.current=a},[a,r])}const u=f.useCallback(l=>{if(a){const h=ry(l)?l(e):l;h!==e&&i.current?.(h)}else o(l)},[a,e,o,i]);return[c,u]}function ny({defaultProp:e,onChange:t}){const[n,r]=f.useState(e),s=f.useRef(n),o=f.useRef(t);return ty(()=>{o.current=t},[t]),f.useEffect(()=>{s.current!==n&&(o.current?.(n),s.current=n)},[n,s]),[n,r,o]}function ry(e){return typeof e=="function"}function sy(e,t=globalThis?.document){const n=Re(e);f.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var oy="DismissableLayer",Jr="dismissableLayer.update",iy="dismissableLayer.pointerDownOutside",ay="dismissableLayer.focusOutside",Fi,rl=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),zn=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:o,onInteractOutside:i,onDismiss:a,...c}=e,u=f.useContext(rl),[l,h]=f.useState(null),p=l?.ownerDocument??globalThis?.document,[,m]=f.useState({}),v=X(t,P=>h(P)),g=Array.from(u.layers),[y]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=g.indexOf(y),b=l?g.indexOf(l):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,S=b>=x,T=uy(P=>{const E=P.target,R=[...u.branches].some(N=>N.contains(E));!S||R||(s?.(P),i?.(P),P.defaultPrevented||a?.())},p),M=dy(P=>{const E=P.target;[...u.branches].some(N=>N.contains(E))||(o?.(P),i?.(P),P.defaultPrevented||a?.())},p);return sy(P=>{b===u.layers.size-1&&(r?.(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},p),f.useEffect(()=>{if(l)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Fi=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(l)),u.layers.add(l),Bi(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Fi)}},[l,p,n,u]),f.useEffect(()=>()=>{l&&(u.layers.delete(l),u.layersWithOutsidePointerEventsDisabled.delete(l),Bi())},[l,u]),f.useEffect(()=>{const P=()=>m({});return document.addEventListener(Jr,P),()=>document.removeEventListener(Jr,P)},[]),d.jsx(G.div,{...c,ref:v,style:{pointerEvents:w?S?"auto":"none":void 0,...e.style},onFocusCapture:_(e.onFocusCapture,M.onFocusCapture),onBlurCapture:_(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:_(e.onPointerDownCapture,T.onPointerDownCapture)})});zn.displayName=oy;var cy="DismissableLayerBranch",ly=f.forwardRef((e,t)=>{const n=f.useContext(rl),r=f.useRef(null),s=X(t,r);return f.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),d.jsx(G.div,{...e,ref:s})});ly.displayName=cy;function uy(e,t=globalThis?.document){const n=Re(e),r=f.useRef(!1),s=f.useRef(()=>{});return f.useEffect(()=>{const o=a=>{if(a.target&&!r.current){let c=function(){sl(iy,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=c,t.addEventListener("click",s.current,{once:!0})):c()}else t.removeEventListener("click",s.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",o),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function dy(e,t=globalThis?.document){const n=Re(e),r=f.useRef(!1);return f.useEffect(()=>{const s=o=>{o.target&&!r.current&&sl(ay,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Bi(){const e=new CustomEvent(Jr);document.dispatchEvent(e)}function sl(e,t,n,{discrete:r}){const s=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?fa(s,o):s.dispatchEvent(o)}var br="focusScope.autoFocusOnMount",wr="focusScope.autoFocusOnUnmount",$i={bubbles:!1,cancelable:!0},fy="FocusScope",Gs=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...i}=e,[a,c]=f.useState(null),u=Re(s),l=Re(o),h=f.useRef(null),p=X(t,g=>c(g)),m=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let g=function(w){if(m.paused||!a)return;const S=w.target;a.contains(S)?h.current=S:_e(h.current,{select:!0})},y=function(w){if(m.paused||!a)return;const S=w.relatedTarget;S!==null&&(a.contains(S)||_e(h.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const T of w)T.removedNodes.length>0&&_e(a)};document.addEventListener("focusin",g),document.addEventListener("focusout",y);const b=new MutationObserver(x);return a&&b.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",y),b.disconnect()}}},[r,a,m.paused]),f.useEffect(()=>{if(a){Wi.add(m);const g=document.activeElement;if(!a.contains(g)){const x=new CustomEvent(br,$i);a.addEventListener(br,u),a.dispatchEvent(x),x.defaultPrevented||(hy(yy(ol(a)),{select:!0}),document.activeElement===g&&_e(a))}return()=>{a.removeEventListener(br,u),setTimeout(()=>{const x=new CustomEvent(wr,$i);a.addEventListener(wr,l),a.dispatchEvent(x),x.defaultPrevented||_e(g??document.body,{select:!0}),a.removeEventListener(wr,l),Wi.remove(m)},0)}}},[a,u,l,m]);const v=f.useCallback(g=>{if(!n&&!r||m.paused)return;const y=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,x=document.activeElement;if(y&&x){const b=g.currentTarget,[w,S]=py(b);w&&S?!g.shiftKey&&x===S?(g.preventDefault(),n&&_e(w,{select:!0})):g.shiftKey&&x===w&&(g.preventDefault(),n&&_e(S,{select:!0})):x===b&&g.preventDefault()}},[n,r,m.paused]);return d.jsx(G.div,{tabIndex:-1,...i,ref:p,onKeyDown:v})});Gs.displayName=fy;function hy(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(_e(r,{select:t}),document.activeElement!==n)return}function py(e){const t=ol(e),n=Ui(t,e),r=Ui(t.reverse(),e);return[n,r]}function ol(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ui(e,t){for(const n of e)if(!my(n,{upTo:t}))return n}function my(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function gy(e){return e instanceof HTMLInputElement&&"select"in e}function _e(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&gy(e)&&t&&e.select()}}var Wi=vy();function vy(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=zi(e,t),e.unshift(t)},remove(t){e=zi(e,t),e[0]?.resume()}}}function zi(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function yy(e){return e.filter(t=>t.tagName!=="A")}var xy="Portal",Ys=f.forwardRef((e,t)=>{const{container:n,...r}=e,[s,o]=f.useState(!1);Ve(()=>o(!0),[]);const i=n||s&&globalThis?.document?.body;return i?nf.createPortal(d.jsx(G.div,{...r,ref:t}),i):null});Ys.displayName=xy;function by(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Ie=e=>{const{present:t,children:n}=e,r=wy(t),s=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),o=X(r.ref,Cy(s));return typeof n=="function"||r.isPresent?f.cloneElement(s,{ref:o}):null};Ie.displayName="Presence";function wy(e){const[t,n]=f.useState(),r=f.useRef(null),s=f.useRef(e),o=f.useRef("none"),i=e?"mounted":"unmounted",[a,c]=by(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const u=un(r.current);o.current=a==="mounted"?u:"none"},[a]),Ve(()=>{const u=r.current,l=s.current;if(l!==e){const p=o.current,m=un(u);e?c("MOUNT"):m==="none"||u?.display==="none"?c("UNMOUNT"):c(l&&p!==m?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,c]),Ve(()=>{if(t){let u;const l=t.ownerDocument.defaultView??window,h=m=>{const g=un(r.current).includes(m.animationName);if(m.target===t&&g&&(c("ANIMATION_END"),!s.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",u=l.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},p=m=>{m.target===t&&(o.current=un(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{l.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:f.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function un(e){return e?.animationName||"none"}function Cy(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Cr=0;function il(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ki()),document.body.insertAdjacentElement("beforeend",e[1]??Ki()),Cr++,()=>{Cr===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Cr--}},[])}function Ki(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var be=function(){return be=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},be.apply(this,arguments)};function al(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function Sy(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,o;r<s;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var bn="right-scroll-bar-position",wn="width-before-scroll-bar",Ty="with-scroll-bars-hidden",Ay="--removed-body-scroll-bar-size";function Sr(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Py(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var Ey=typeof window<"u"?f.useLayoutEffect:f.useEffect,Hi=new WeakMap;function Ry(e,t){var n=Py(null,function(r){return e.forEach(function(s){return Sr(s,r)})});return Ey(function(){var r=Hi.get(n);if(r){var s=new Set(r),o=new Set(e),i=n.current;s.forEach(function(a){o.has(a)||Sr(a,null)}),o.forEach(function(a){s.has(a)||Sr(a,i)})}Hi.set(n,e)},[e]),n}function My(e){return e}function Dy(e,t){t===void 0&&(t=My);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(o){var i=t(o,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(o){for(r=!0;n.length;){var i=n;n=[],i.forEach(o)}n={push:function(a){return o(a)},filter:function(){return n}}},assignMedium:function(o){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(o),i=n}var c=function(){var l=i;i=[],l.forEach(o)},u=function(){return Promise.resolve().then(c)};u(),n={push:function(l){i.push(l),u()},filter:function(l){return i=i.filter(l),n}}}};return s}function Ny(e){e===void 0&&(e={});var t=Dy(null);return t.options=be({async:!0,ssr:!1},e),t}var cl=function(e){var t=e.sideCar,n=al(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,be({},n))};cl.isSideCarExport=!0;function jy(e,t){return e.useMedium(t),cl}var ll=Ny(),Tr=function(){},Kn=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:Tr,onWheelCapture:Tr,onTouchMoveCapture:Tr}),s=r[0],o=r[1],i=e.forwardProps,a=e.children,c=e.className,u=e.removeScrollBar,l=e.enabled,h=e.shards,p=e.sideCar,m=e.noRelative,v=e.noIsolation,g=e.inert,y=e.allowPinchZoom,x=e.as,b=x===void 0?"div":x,w=e.gapMode,S=al(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=p,M=Ry([n,t]),P=be(be({},S),s);return f.createElement(f.Fragment,null,l&&f.createElement(T,{sideCar:ll,removeScrollBar:u,shards:h,noRelative:m,noIsolation:v,inert:g,setCallbacks:o,allowPinchZoom:!!y,lockRef:n,gapMode:w}),i?f.cloneElement(f.Children.only(a),be(be({},P),{ref:M})):f.createElement(b,be({},P,{className:c,ref:M}),a))});Kn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Kn.classNames={fullWidth:wn,zeroRight:bn};var Iy=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function ky(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Iy();return t&&e.setAttribute("nonce",t),e}function Oy(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function _y(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Ly=function(){var e=0,t=null;return{add:function(n){e==0&&(t=ky())&&(Oy(t,n),_y(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Vy=function(){var e=Ly();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ul=function(){var e=Vy(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},Fy={left:0,top:0,right:0,gap:0},Ar=function(e){return parseInt(e||"",10)||0},By=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[Ar(n),Ar(r),Ar(s)]},$y=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Fy;var t=By(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Uy=ul(),ft="data-scroll-locked",Wy=function(e,t,n,r){var s=e.left,o=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ty,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(ft,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(bn,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(wn,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(bn," .").concat(bn,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(wn," .").concat(wn,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ft,`] {
    `).concat(Ay,": ").concat(a,`px;
  }
`)},Gi=function(){var e=parseInt(document.body.getAttribute(ft)||"0",10);return isFinite(e)?e:0},zy=function(){f.useEffect(function(){return document.body.setAttribute(ft,(Gi()+1).toString()),function(){var e=Gi()-1;e<=0?document.body.removeAttribute(ft):document.body.setAttribute(ft,e.toString())}},[])},Ky=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;zy();var o=f.useMemo(function(){return $y(s)},[s]);return f.createElement(Uy,{styles:Wy(o,!t,s,n?"":"!important")})},Qr=!1;if(typeof window<"u")try{var dn=Object.defineProperty({},"passive",{get:function(){return Qr=!0,!0}});window.addEventListener("test",dn,dn),window.removeEventListener("test",dn,dn)}catch{Qr=!1}var st=Qr?{passive:!1}:!1,Hy=function(e){return e.tagName==="TEXTAREA"},dl=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Hy(e)&&n[t]==="visible")},Gy=function(e){return dl(e,"overflowY")},Yy=function(e){return dl(e,"overflowX")},Yi=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=fl(e,r);if(s){var o=hl(e,r),i=o[1],a=o[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Xy=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},qy=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},fl=function(e,t){return e==="v"?Gy(t):Yy(t)},hl=function(e,t){return e==="v"?Xy(t):qy(t)},Zy=function(e,t){return e==="h"&&t==="rtl"?-1:1},Jy=function(e,t,n,r,s){var o=Zy(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,c=t.contains(a),u=!1,l=i>0,h=0,p=0;do{if(!a)break;var m=hl(e,a),v=m[0],g=m[1],y=m[2],x=g-y-o*v;(v||x)&&fl(e,a)&&(h+=x,p+=v);var b=a.parentNode;a=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(l&&Math.abs(h)<1||!l&&Math.abs(p)<1)&&(u=!0),u},fn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Xi=function(e){return[e.deltaX,e.deltaY]},qi=function(e){return e&&"current"in e?e.current:e},Qy=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ex=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},tx=0,ot=[];function nx(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),s=f.useState(tx++)[0],o=f.useState(ul)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var g=Sy([e.lockRef.current],(e.shards||[]).map(qi),!0).filter(Boolean);return g.forEach(function(y){return y.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),g.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(g,y){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var x=fn(g),b=n.current,w="deltaX"in g?g.deltaX:b[0]-x[0],S="deltaY"in g?g.deltaY:b[1]-x[1],T,M=g.target,P=Math.abs(w)>Math.abs(S)?"h":"v";if("touches"in g&&P==="h"&&M.type==="range")return!1;var E=Yi(P,M);if(!E)return!0;if(E?T=P:(T=P==="v"?"h":"v",E=Yi(P,M)),!E)return!1;if(!r.current&&"changedTouches"in g&&(w||S)&&(r.current=T),!T)return!0;var R=r.current||T;return Jy(R,y,g,R==="h"?w:S)},[]),c=f.useCallback(function(g){var y=g;if(!(!ot.length||ot[ot.length-1]!==o)){var x="deltaY"in y?Xi(y):fn(y),b=t.current.filter(function(T){return T.name===y.type&&(T.target===y.target||y.target===T.shadowParent)&&Qy(T.delta,x)})[0];if(b&&b.should){y.cancelable&&y.preventDefault();return}if(!b){var w=(i.current.shards||[]).map(qi).filter(Boolean).filter(function(T){return T.contains(y.target)}),S=w.length>0?a(y,w[0]):!i.current.noIsolation;S&&y.cancelable&&y.preventDefault()}}},[]),u=f.useCallback(function(g,y,x,b){var w={name:g,delta:y,target:x,should:b,shadowParent:rx(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(S){return S!==w})},1)},[]),l=f.useCallback(function(g){n.current=fn(g),r.current=void 0},[]),h=f.useCallback(function(g){u(g.type,Xi(g),g.target,a(g,e.lockRef.current))},[]),p=f.useCallback(function(g){u(g.type,fn(g),g.target,a(g,e.lockRef.current))},[]);f.useEffect(function(){return ot.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",c,st),document.addEventListener("touchmove",c,st),document.addEventListener("touchstart",l,st),function(){ot=ot.filter(function(g){return g!==o}),document.removeEventListener("wheel",c,st),document.removeEventListener("touchmove",c,st),document.removeEventListener("touchstart",l,st)}},[]);var m=e.removeScrollBar,v=e.inert;return f.createElement(f.Fragment,null,v?f.createElement(o,{styles:ex(s)}):null,m?f.createElement(Ky,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function rx(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const sx=jy(ll,nx);var Xs=f.forwardRef(function(e,t){return f.createElement(Kn,be({},e,{ref:t,sideCar:sx}))});Xs.classNames=Kn.classNames;var ox=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},it=new WeakMap,hn=new WeakMap,pn={},Pr=0,pl=function(e){return e&&(e.host||pl(e.parentNode))},ix=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=pl(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},ax=function(e,t,n,r){var s=ix(t,Array.isArray(e)?e:[e]);pn[n]||(pn[n]=new WeakMap);var o=pn[n],i=[],a=new Set,c=new Set(s),u=function(h){!h||a.has(h)||(a.add(h),u(h.parentNode))};s.forEach(u);var l=function(h){!h||c.has(h)||Array.prototype.forEach.call(h.children,function(p){if(a.has(p))l(p);else try{var m=p.getAttribute(r),v=m!==null&&m!=="false",g=(it.get(p)||0)+1,y=(o.get(p)||0)+1;it.set(p,g),o.set(p,y),i.push(p),g===1&&v&&hn.set(p,!0),y===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return l(t),a.clear(),Pr++,function(){i.forEach(function(h){var p=it.get(h)-1,m=o.get(h)-1;it.set(h,p),o.set(h,m),p||(hn.has(h)||h.removeAttribute(r),hn.delete(h)),m||h.removeAttribute(n)}),Pr--,Pr||(it=new WeakMap,it=new WeakMap,hn=new WeakMap,pn={})}},ml=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=ox(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),ax(r,s,n,"aria-hidden")):function(){return null}},Hn="Dialog",[gl,vl]=We(Hn),[cx,ve]=gl(Hn),yl=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:o,modal:i=!0}=e,a=f.useRef(null),c=f.useRef(null),[u,l]=Wn({prop:r,defaultProp:s??!1,onChange:o,caller:Hn});return d.jsx(cx,{scope:t,triggerRef:a,contentRef:c,contentId:Qe(),titleId:Qe(),descriptionId:Qe(),open:u,onOpenChange:l,onOpenToggle:f.useCallback(()=>l(h=>!h),[l]),modal:i,children:n})};yl.displayName=Hn;var xl="DialogTrigger",bl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=ve(xl,n),o=X(t,s.triggerRef);return d.jsx(G.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Js(s.open),...r,ref:o,onClick:_(e.onClick,s.onOpenToggle)})});bl.displayName=xl;var qs="DialogPortal",[lx,wl]=gl(qs,{forceMount:void 0}),Cl=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:s}=e,o=ve(qs,t);return d.jsx(lx,{scope:t,forceMount:n,children:f.Children.map(r,i=>d.jsx(Ie,{present:n||o.open,children:d.jsx(Ys,{asChild:!0,container:s,children:i})}))})};Cl.displayName=qs;var Dn="DialogOverlay",Sl=f.forwardRef((e,t)=>{const n=wl(Dn,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=ve(Dn,e.__scopeDialog);return o.modal?d.jsx(Ie,{present:r||o.open,children:d.jsx(dx,{...s,ref:t})}):null});Sl.displayName=Dn;var ux=Sn("DialogOverlay.RemoveScroll"),dx=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=ve(Dn,n);return d.jsx(Xs,{as:ux,allowPinchZoom:!0,shards:[s.contentRef],children:d.jsx(G.div,{"data-state":Js(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),et="DialogContent",Tl=f.forwardRef((e,t)=>{const n=wl(et,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=ve(et,e.__scopeDialog);return d.jsx(Ie,{present:r||o.open,children:o.modal?d.jsx(fx,{...s,ref:t}):d.jsx(hx,{...s,ref:t})})});Tl.displayName=et;var fx=f.forwardRef((e,t)=>{const n=ve(et,e.__scopeDialog),r=f.useRef(null),s=X(t,n.contentRef,r);return f.useEffect(()=>{const o=r.current;if(o)return ml(o)},[]),d.jsx(Al,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:_(e.onCloseAutoFocus,o=>{o.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:_(e.onPointerDownOutside,o=>{const i=o.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&o.preventDefault()}),onFocusOutside:_(e.onFocusOutside,o=>o.preventDefault())})}),hx=f.forwardRef((e,t)=>{const n=ve(et,e.__scopeDialog),r=f.useRef(!1),s=f.useRef(!1);return d.jsx(Al,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{e.onCloseAutoFocus?.(o),o.defaultPrevented||(r.current||n.triggerRef.current?.focus(),o.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:o=>{e.onInteractOutside?.(o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const i=o.target;n.triggerRef.current?.contains(i)&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&s.current&&o.preventDefault()}})}),Al=f.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:o,...i}=e,a=ve(et,n),c=f.useRef(null),u=X(t,c);return il(),d.jsxs(d.Fragment,{children:[d.jsx(Gs,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:o,children:d.jsx(zn,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Js(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),d.jsxs(d.Fragment,{children:[d.jsx(mx,{titleId:a.titleId}),d.jsx(vx,{contentRef:c,descriptionId:a.descriptionId})]})]})}),Zs="DialogTitle",Pl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=ve(Zs,n);return d.jsx(G.h2,{id:s.titleId,...r,ref:t})});Pl.displayName=Zs;var El="DialogDescription",Rl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=ve(El,n);return d.jsx(G.p,{id:s.descriptionId,...r,ref:t})});Rl.displayName=El;var Ml="DialogClose",Dl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=ve(Ml,n);return d.jsx(G.button,{type:"button",...r,ref:t,onClick:_(e.onClick,()=>s.onOpenChange(!1))})});Dl.displayName=Ml;function Js(e){return e?"open":"closed"}var Nl="DialogTitleWarning",[px,jl]=of(Nl,{contentName:et,titleName:Zs,docsSlug:"dialog"}),mx=({titleId:e})=>{const t=jl(Nl),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return f.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},gx="DialogDescriptionWarning",vx=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${jl(gx).contentName}}.`;return f.useEffect(()=>{const s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Il=yl,yx=bl,kl=Cl,Qs=Sl,eo=Tl,to=Pl,no=Rl,ro=Dl;const xx=Il,bx=kl,Ol=f.forwardRef(({className:e,...t},n)=>d.jsx(Qs,{className:j("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));Ol.displayName=Qs.displayName;const wx=us("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),_l=f.forwardRef(({side:e="right",className:t,children:n,...r},s)=>d.jsxs(bx,{children:[d.jsx(Ol,{}),d.jsxs(eo,{ref:s,className:j(wx({side:e}),t),...r,children:[n,d.jsxs(ro,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[d.jsx(Ln,{className:"h-4 w-4"}),d.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));_l.displayName=eo.displayName;const Cx=f.forwardRef(({className:e,...t},n)=>d.jsx(to,{ref:n,className:j("text-lg font-semibold text-foreground",e),...t}));Cx.displayName=to.displayName;const Sx=f.forwardRef(({className:e,...t},n)=>d.jsx(no,{ref:n,className:j("text-sm text-muted-foreground",e),...t}));Sx.displayName=no.displayName;function Zi({className:e,...t}){return d.jsx("div",{className:j("animate-pulse rounded-md bg-muted",e),...t})}const Tx=["top","right","bottom","left"],$e=Math.min,ie=Math.max,Nn=Math.round,mn=Math.floor,Ae=e=>({x:e,y:e}),Ax={left:"right",right:"left",bottom:"top",top:"bottom"},Px={start:"end",end:"start"};function es(e,t,n){return ie(e,$e(t,n))}function Ne(e,t){return typeof e=="function"?e(t):e}function je(e){return e.split("-")[0]}function Ct(e){return e.split("-")[1]}function so(e){return e==="x"?"y":"x"}function oo(e){return e==="y"?"height":"width"}const Ex=new Set(["top","bottom"]);function we(e){return Ex.has(je(e))?"y":"x"}function io(e){return so(we(e))}function Rx(e,t,n){n===void 0&&(n=!1);const r=Ct(e),s=io(e),o=oo(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=jn(i)),[i,jn(i)]}function Mx(e){const t=jn(e);return[ts(e),t,ts(t)]}function ts(e){return e.replace(/start|end/g,t=>Px[t])}const Ji=["left","right"],Qi=["right","left"],Dx=["top","bottom"],Nx=["bottom","top"];function jx(e,t,n){switch(e){case"top":case"bottom":return n?t?Qi:Ji:t?Ji:Qi;case"left":case"right":return t?Dx:Nx;default:return[]}}function Ix(e,t,n,r){const s=Ct(e);let o=jx(je(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(ts)))),o}function jn(e){return e.replace(/left|right|bottom|top/g,t=>Ax[t])}function kx(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ll(e){return typeof e!="number"?kx(e):{top:e,right:e,bottom:e,left:e}}function In(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function ea(e,t,n){let{reference:r,floating:s}=e;const o=we(t),i=io(t),a=oo(i),c=je(t),u=o==="y",l=r.x+r.width/2-s.width/2,h=r.y+r.height/2-s.height/2,p=r[a]/2-s[a]/2;let m;switch(c){case"top":m={x:l,y:r.y-s.height};break;case"bottom":m={x:l,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:h};break;case"left":m={x:r.x-s.width,y:h};break;default:m={x:r.x,y:r.y}}switch(Ct(t)){case"start":m[i]-=p*(n&&u?-1:1);break;case"end":m[i]+=p*(n&&u?-1:1);break}return m}const Ox=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),c=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:l,y:h}=ea(u,r,c),p=r,m={},v=0;for(let g=0;g<a.length;g++){const{name:y,fn:x}=a[g],{x:b,y:w,data:S,reset:T}=await x({x:l,y:h,initialPlacement:r,placement:p,strategy:s,middlewareData:m,rects:u,platform:i,elements:{reference:e,floating:t}});l=b??l,h=w??h,m={...m,[y]:{...m[y],...S}},T&&v<=50&&(v++,typeof T=="object"&&(T.placement&&(p=T.placement),T.rects&&(u=T.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):T.rects),{x:l,y:h}=ea(u,p,c)),g=-1)}return{x:l,y:h,placement:p,strategy:s,middlewareData:m}};async function Ut(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:l="viewport",elementContext:h="floating",altBoundary:p=!1,padding:m=0}=Ne(t,e),v=Ll(m),y=a[p?h==="floating"?"reference":"floating":h],x=In(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(y)))==null||n?y:y.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:l,strategy:c})),b=h==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,w=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),S=await(o.isElement==null?void 0:o.isElement(w))?await(o.getScale==null?void 0:o.getScale(w))||{x:1,y:1}:{x:1,y:1},T=In(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:w,strategy:c}):b);return{top:(x.top-T.top+v.top)/S.y,bottom:(T.bottom-x.bottom+v.bottom)/S.y,left:(x.left-T.left+v.left)/S.x,right:(T.right-x.right+v.right)/S.x}}const _x=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:c}=t,{element:u,padding:l=0}=Ne(e,t)||{};if(u==null)return{};const h=Ll(l),p={x:n,y:r},m=io(s),v=oo(m),g=await i.getDimensions(u),y=m==="y",x=y?"top":"left",b=y?"bottom":"right",w=y?"clientHeight":"clientWidth",S=o.reference[v]+o.reference[m]-p[m]-o.floating[v],T=p[m]-o.reference[m],M=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let P=M?M[w]:0;(!P||!await(i.isElement==null?void 0:i.isElement(M)))&&(P=a.floating[w]||o.floating[v]);const E=S/2-T/2,R=P/2-g[v]/2-1,N=$e(h[x],R),O=$e(h[b],R),V=N,B=P-g[v]-O,L=P/2-g[v]/2+E,W=es(V,L,B),F=!c.arrow&&Ct(s)!=null&&L!==W&&o.reference[v]/2-(L<V?N:O)-g[v]/2<0,D=F?L<V?L-V:L-B:0;return{[m]:p[m]+D,data:{[m]:W,centerOffset:L-W-D,...F&&{alignmentOffset:D}},reset:F}}}),Lx=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:c,elements:u}=t,{mainAxis:l=!0,crossAxis:h=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:g=!0,...y}=Ne(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const x=je(s),b=we(a),w=je(a)===a,S=await(c.isRTL==null?void 0:c.isRTL(u.floating)),T=p||(w||!g?[jn(a)]:Mx(a)),M=v!=="none";!p&&M&&T.push(...Ix(a,g,v,S));const P=[a,...T],E=await Ut(t,y),R=[];let N=((r=o.flip)==null?void 0:r.overflows)||[];if(l&&R.push(E[x]),h){const L=Rx(s,i,S);R.push(E[L[0]],E[L[1]])}if(N=[...N,{placement:s,overflows:R}],!R.every(L=>L<=0)){var O,V;const L=(((O=o.flip)==null?void 0:O.index)||0)+1,W=P[L];if(W&&(!(h==="alignment"?b!==we(W):!1)||N.every(C=>C.overflows[0]>0&&we(C.placement)===b)))return{data:{index:L,overflows:N},reset:{placement:W}};let F=(V=N.filter(D=>D.overflows[0]<=0).sort((D,C)=>D.overflows[1]-C.overflows[1])[0])==null?void 0:V.placement;if(!F)switch(m){case"bestFit":{var B;const D=(B=N.filter(C=>{if(M){const A=we(C.placement);return A===b||A==="y"}return!0}).map(C=>[C.placement,C.overflows.filter(A=>A>0).reduce((A,I)=>A+I,0)]).sort((C,A)=>C[1]-A[1])[0])==null?void 0:B[0];D&&(F=D);break}case"initialPlacement":F=a;break}if(s!==F)return{reset:{placement:F}}}return{}}}};function ta(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function na(e){return Tx.some(t=>e[t]>=0)}const Vx=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Ne(e,t);switch(r){case"referenceHidden":{const o=await Ut(t,{...s,elementContext:"reference"}),i=ta(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:na(i)}}}case"escaped":{const o=await Ut(t,{...s,altBoundary:!0}),i=ta(o,n.floating);return{data:{escapedOffsets:i,escaped:na(i)}}}default:return{}}}}},Vl=new Set(["left","top"]);async function Fx(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=je(n),a=Ct(n),c=we(n)==="y",u=Vl.has(i)?-1:1,l=o&&c?-1:1,h=Ne(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:v}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&typeof v=="number"&&(m=a==="end"?v*-1:v),c?{x:m*l,y:p*u}:{x:p*u,y:m*l}}const Bx=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,c=await Fx(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+c.x,y:o+c.y,data:{...c,placement:i}}}}},$x=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:y=>{let{x,y:b}=y;return{x,y:b}}},...c}=Ne(e,t),u={x:n,y:r},l=await Ut(t,c),h=we(je(s)),p=so(h);let m=u[p],v=u[h];if(o){const y=p==="y"?"top":"left",x=p==="y"?"bottom":"right",b=m+l[y],w=m-l[x];m=es(b,m,w)}if(i){const y=h==="y"?"top":"left",x=h==="y"?"bottom":"right",b=v+l[y],w=v-l[x];v=es(b,v,w)}const g=a.fn({...t,[p]:m,[h]:v});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[p]:o,[h]:i}}}}}},Ux=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:o,middlewareData:i}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=Ne(e,t),l={x:n,y:r},h=we(s),p=so(h);let m=l[p],v=l[h];const g=Ne(a,t),y=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(c){const w=p==="y"?"height":"width",S=o.reference[p]-o.floating[w]+y.mainAxis,T=o.reference[p]+o.reference[w]-y.mainAxis;m<S?m=S:m>T&&(m=T)}if(u){var x,b;const w=p==="y"?"width":"height",S=Vl.has(je(s)),T=o.reference[h]-o.floating[w]+(S&&((x=i.offset)==null?void 0:x[h])||0)+(S?0:y.crossAxis),M=o.reference[h]+o.reference[w]+(S?0:((b=i.offset)==null?void 0:b[h])||0)-(S?y.crossAxis:0);v<T?v=T:v>M&&(v=M)}return{[p]:m,[h]:v}}}},Wx=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:o,platform:i,elements:a}=t,{apply:c=()=>{},...u}=Ne(e,t),l=await Ut(t,u),h=je(s),p=Ct(s),m=we(s)==="y",{width:v,height:g}=o.floating;let y,x;h==="top"||h==="bottom"?(y=h,x=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=h,y=p==="end"?"top":"bottom");const b=g-l.top-l.bottom,w=v-l.left-l.right,S=$e(g-l[y],b),T=$e(v-l[x],w),M=!t.middlewareData.shift;let P=S,E=T;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(E=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=b),M&&!p){const N=ie(l.left,0),O=ie(l.right,0),V=ie(l.top,0),B=ie(l.bottom,0);m?E=v-2*(N!==0||O!==0?N+O:ie(l.left,l.right)):P=g-2*(V!==0||B!==0?V+B:ie(l.top,l.bottom))}await c({...t,availableWidth:E,availableHeight:P});const R=await i.getDimensions(a.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}};function Gn(){return typeof window<"u"}function St(e){return Fl(e)?(e.nodeName||"").toLowerCase():"#document"}function ce(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ee(e){var t;return(t=(Fl(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Fl(e){return Gn()?e instanceof Node||e instanceof ce(e).Node:!1}function me(e){return Gn()?e instanceof Element||e instanceof ce(e).Element:!1}function Pe(e){return Gn()?e instanceof HTMLElement||e instanceof ce(e).HTMLElement:!1}function ra(e){return!Gn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ce(e).ShadowRoot}const zx=new Set(["inline","contents"]);function Qt(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=ge(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!zx.has(s)}const Kx=new Set(["table","td","th"]);function Hx(e){return Kx.has(St(e))}const Gx=[":popover-open",":modal"];function Yn(e){return Gx.some(t=>{try{return e.matches(t)}catch{return!1}})}const Yx=["transform","translate","scale","rotate","perspective"],Xx=["transform","translate","scale","rotate","perspective","filter"],qx=["paint","layout","strict","content"];function ao(e){const t=co(),n=me(e)?ge(e):e;return Yx.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Xx.some(r=>(n.willChange||"").includes(r))||qx.some(r=>(n.contain||"").includes(r))}function Zx(e){let t=Ue(e);for(;Pe(t)&&!vt(t);){if(ao(t))return t;if(Yn(t))return null;t=Ue(t)}return null}function co(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Jx=new Set(["html","body","#document"]);function vt(e){return Jx.has(St(e))}function ge(e){return ce(e).getComputedStyle(e)}function Xn(e){return me(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ue(e){if(St(e)==="html")return e;const t=e.assignedSlot||e.parentNode||ra(e)&&e.host||Ee(e);return ra(t)?t.host:t}function Bl(e){const t=Ue(e);return vt(t)?e.ownerDocument?e.ownerDocument.body:e.body:Pe(t)&&Qt(t)?t:Bl(t)}function Wt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=Bl(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=ce(s);if(o){const a=ns(i);return t.concat(i,i.visualViewport||[],Qt(s)?s:[],a&&n?Wt(a):[])}return t.concat(s,Wt(s,[],n))}function ns(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $l(e){const t=ge(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Pe(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=Nn(n)!==o||Nn(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function lo(e){return me(e)?e:e.contextElement}function ht(e){const t=lo(e);if(!Pe(t))return Ae(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=$l(t);let i=(o?Nn(n.width):n.width)/r,a=(o?Nn(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Qx=Ae(0);function Ul(e){const t=ce(e);return!co()||!t.visualViewport?Qx:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function eb(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==ce(e)?!1:t}function tt(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=lo(e);let i=Ae(1);t&&(r?me(r)&&(i=ht(r)):i=ht(e));const a=eb(o,n,r)?Ul(o):Ae(0);let c=(s.left+a.x)/i.x,u=(s.top+a.y)/i.y,l=s.width/i.x,h=s.height/i.y;if(o){const p=ce(o),m=r&&me(r)?ce(r):r;let v=p,g=ns(v);for(;g&&r&&m!==v;){const y=ht(g),x=g.getBoundingClientRect(),b=ge(g),w=x.left+(g.clientLeft+parseFloat(b.paddingLeft))*y.x,S=x.top+(g.clientTop+parseFloat(b.paddingTop))*y.y;c*=y.x,u*=y.y,l*=y.x,h*=y.y,c+=w,u+=S,v=ce(g),g=ns(v)}}return In({width:l,height:h,x:c,y:u})}function uo(e,t){const n=Xn(e).scrollLeft;return t?t.left+n:tt(Ee(e)).left+n}function Wl(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:uo(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function tb(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=Ee(r),a=t?Yn(t.floating):!1;if(r===i||a&&o)return n;let c={scrollLeft:0,scrollTop:0},u=Ae(1);const l=Ae(0),h=Pe(r);if((h||!h&&!o)&&((St(r)!=="body"||Qt(i))&&(c=Xn(r)),Pe(r))){const m=tt(r);u=ht(r),l.x=m.x+r.clientLeft,l.y=m.y+r.clientTop}const p=i&&!h&&!o?Wl(i,c,!0):Ae(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+l.x+p.x,y:n.y*u.y-c.scrollTop*u.y+l.y+p.y}}function nb(e){return Array.from(e.getClientRects())}function rb(e){const t=Ee(e),n=Xn(e),r=e.ownerDocument.body,s=ie(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=ie(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+uo(e);const a=-n.scrollTop;return ge(r).direction==="rtl"&&(i+=ie(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function sb(e,t){const n=ce(e),r=Ee(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,c=0;if(s){o=s.width,i=s.height;const u=co();(!u||u&&t==="fixed")&&(a=s.offsetLeft,c=s.offsetTop)}return{width:o,height:i,x:a,y:c}}const ob=new Set(["absolute","fixed"]);function ib(e,t){const n=tt(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=Pe(e)?ht(e):Ae(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,c=s*o.x,u=r*o.y;return{width:i,height:a,x:c,y:u}}function sa(e,t,n){let r;if(t==="viewport")r=sb(e,n);else if(t==="document")r=rb(Ee(e));else if(me(t))r=ib(t,n);else{const s=Ul(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return In(r)}function zl(e,t){const n=Ue(e);return n===t||!me(n)||vt(n)?!1:ge(n).position==="fixed"||zl(n,t)}function ab(e,t){const n=t.get(e);if(n)return n;let r=Wt(e,[],!1).filter(a=>me(a)&&St(a)!=="body"),s=null;const o=ge(e).position==="fixed";let i=o?Ue(e):e;for(;me(i)&&!vt(i);){const a=ge(i),c=ao(i);!c&&a.position==="fixed"&&(s=null),(o?!c&&!s:!c&&a.position==="static"&&!!s&&ob.has(s.position)||Qt(i)&&!c&&zl(e,i))?r=r.filter(l=>l!==i):s=a,i=Ue(i)}return t.set(e,r),r}function cb(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Yn(t)?[]:ab(t,this._c):[].concat(n),r],a=i[0],c=i.reduce((u,l)=>{const h=sa(t,l,s);return u.top=ie(h.top,u.top),u.right=$e(h.right,u.right),u.bottom=$e(h.bottom,u.bottom),u.left=ie(h.left,u.left),u},sa(t,a,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function lb(e){const{width:t,height:n}=$l(e);return{width:t,height:n}}function ub(e,t,n){const r=Pe(t),s=Ee(t),o=n==="fixed",i=tt(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const c=Ae(0);function u(){c.x=uo(s)}if(r||!r&&!o)if((St(t)!=="body"||Qt(s))&&(a=Xn(t)),r){const m=tt(t,!0,o,t);c.x=m.x+t.clientLeft,c.y=m.y+t.clientTop}else s&&u();o&&!r&&s&&u();const l=s&&!r&&!o?Wl(s,a):Ae(0),h=i.left+a.scrollLeft-c.x-l.x,p=i.top+a.scrollTop-c.y-l.y;return{x:h,y:p,width:i.width,height:i.height}}function Er(e){return ge(e).position==="static"}function oa(e,t){if(!Pe(e)||ge(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Ee(e)===n&&(n=n.ownerDocument.body),n}function Kl(e,t){const n=ce(e);if(Yn(e))return n;if(!Pe(e)){let s=Ue(e);for(;s&&!vt(s);){if(me(s)&&!Er(s))return s;s=Ue(s)}return n}let r=oa(e,t);for(;r&&Hx(r)&&Er(r);)r=oa(r,t);return r&&vt(r)&&Er(r)&&!ao(r)?n:r||Zx(e)||n}const db=async function(e){const t=this.getOffsetParent||Kl,n=this.getDimensions,r=await n(e.floating);return{reference:ub(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function fb(e){return ge(e).direction==="rtl"}const hb={convertOffsetParentRelativeRectToViewportRelativeRect:tb,getDocumentElement:Ee,getClippingRect:cb,getOffsetParent:Kl,getElementRects:db,getClientRects:nb,getDimensions:lb,getScale:ht,isElement:me,isRTL:fb};function Hl(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function pb(e,t){let n=null,r;const s=Ee(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),o();const u=e.getBoundingClientRect(),{left:l,top:h,width:p,height:m}=u;if(a||t(),!p||!m)return;const v=mn(h),g=mn(s.clientWidth-(l+p)),y=mn(s.clientHeight-(h+m)),x=mn(l),w={rootMargin:-v+"px "+-g+"px "+-y+"px "+-x+"px",threshold:ie(0,$e(1,c))||1};let S=!0;function T(M){const P=M[0].intersectionRatio;if(P!==c){if(!S)return i();P?i(!1,P):r=setTimeout(()=>{i(!1,1e-7)},1e3)}P===1&&!Hl(u,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(T,{...w,root:s.ownerDocument})}catch{n=new IntersectionObserver(T,w)}n.observe(e)}return i(!0),o}function mb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,u=lo(e),l=s||o?[...u?Wt(u):[],...Wt(t)]:[];l.forEach(x=>{s&&x.addEventListener("scroll",n,{passive:!0}),o&&x.addEventListener("resize",n)});const h=u&&a?pb(u,n):null;let p=-1,m=null;i&&(m=new ResizeObserver(x=>{let[b]=x;b&&b.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var w;(w=m)==null||w.observe(t)})),n()}),u&&!c&&m.observe(u),m.observe(t));let v,g=c?tt(e):null;c&&y();function y(){const x=tt(e);g&&!Hl(g,x)&&n(),g=x,v=requestAnimationFrame(y)}return n(),()=>{var x;l.forEach(b=>{s&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),h?.(),(x=m)==null||x.disconnect(),m=null,c&&cancelAnimationFrame(v)}}const gb=Bx,vb=$x,yb=Lx,xb=Wx,bb=Vx,ia=_x,wb=Ux,Cb=(e,t,n)=>{const r=new Map,s={platform:hb,...n},o={...s.platform,_c:r};return Ox(e,t,{...s,platform:o})};var Sb=typeof document<"u",Tb=function(){},Cn=Sb?f.useLayoutEffect:Tb;function kn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!kn(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&e.$$typeof)&&!kn(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function Gl(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function aa(e,t){const n=Gl(e);return Math.round(t*n)/n}function Rr(e){const t=f.useRef(e);return Cn(()=>{t.current=e}),t}function Ab(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:i}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[l,h]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=f.useState(r);kn(p,r)||m(r);const[v,g]=f.useState(null),[y,x]=f.useState(null),b=f.useCallback(C=>{C!==M.current&&(M.current=C,g(C))},[]),w=f.useCallback(C=>{C!==P.current&&(P.current=C,x(C))},[]),S=o||v,T=i||y,M=f.useRef(null),P=f.useRef(null),E=f.useRef(l),R=c!=null,N=Rr(c),O=Rr(s),V=Rr(u),B=f.useCallback(()=>{if(!M.current||!P.current)return;const C={placement:t,strategy:n,middleware:p};O.current&&(C.platform=O.current),Cb(M.current,P.current,C).then(A=>{const I={...A,isPositioned:V.current!==!1};L.current&&!kn(E.current,I)&&(E.current=I,rf.flushSync(()=>{h(I)}))})},[p,t,n,O,V]);Cn(()=>{u===!1&&E.current.isPositioned&&(E.current.isPositioned=!1,h(C=>({...C,isPositioned:!1})))},[u]);const L=f.useRef(!1);Cn(()=>(L.current=!0,()=>{L.current=!1}),[]),Cn(()=>{if(S&&(M.current=S),T&&(P.current=T),S&&T){if(N.current)return N.current(S,T,B);B()}},[S,T,B,N,R]);const W=f.useMemo(()=>({reference:M,floating:P,setReference:b,setFloating:w}),[b,w]),F=f.useMemo(()=>({reference:S,floating:T}),[S,T]),D=f.useMemo(()=>{const C={position:n,left:0,top:0};if(!F.floating)return C;const A=aa(F.floating,l.x),I=aa(F.floating,l.y);return a?{...C,transform:"translate("+A+"px, "+I+"px)",...Gl(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:I}},[n,a,F.floating,l.x,l.y]);return f.useMemo(()=>({...l,update:B,refs:W,elements:F,floatingStyles:D}),[l,B,W,F,D])}const Pb=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ia({element:r.current,padding:s}).fn(n):{}:r?ia({element:r,padding:s}).fn(n):{}}}},Eb=(e,t)=>({...gb(e),options:[e,t]}),Rb=(e,t)=>({...vb(e),options:[e,t]}),Mb=(e,t)=>({...wb(e),options:[e,t]}),Db=(e,t)=>({...yb(e),options:[e,t]}),Nb=(e,t)=>({...xb(e),options:[e,t]}),jb=(e,t)=>({...bb(e),options:[e,t]}),Ib=(e,t)=>({...Pb(e),options:[e,t]});var kb="Arrow",Yl=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...o}=e;return d.jsx(G.svg,{...o,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:d.jsx("polygon",{points:"0,0 30,0 15,10"})})});Yl.displayName=kb;var Ob=Yl;function _b(e){const[t,n]=f.useState(void 0);return Ve(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const o=s[0];let i,a;if("borderBoxSize"in o){const c=o.borderBoxSize,u=Array.isArray(c)?c[0]:c;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var fo="Popper",[Xl,qn]=We(fo),[Lb,ql]=Xl(fo),Zl=e=>{const{__scopePopper:t,children:n}=e,[r,s]=f.useState(null);return d.jsx(Lb,{scope:t,anchor:r,onAnchorChange:s,children:n})};Zl.displayName=fo;var Jl="PopperAnchor",Ql=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,o=ql(Jl,n),i=f.useRef(null),a=X(t,i);return f.useEffect(()=>{o.onAnchorChange(r?.current||i.current)}),r?null:d.jsx(G.div,{...s,ref:a})});Ql.displayName=Jl;var ho="PopperContent",[Vb,Fb]=Xl(ho),eu=f.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:o="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:l=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:v,...g}=e,y=ql(ho,n),[x,b]=f.useState(null),w=X(t,J=>b(J)),[S,T]=f.useState(null),M=_b(S),P=M?.width??0,E=M?.height??0,R=r+(o!=="center"?"-"+o:""),N=typeof l=="number"?l:{top:0,right:0,bottom:0,left:0,...l},O=Array.isArray(u)?u:[u],V=O.length>0,B={padding:N,boundary:O.filter($b),altBoundary:V},{refs:L,floatingStyles:W,placement:F,isPositioned:D,middlewareData:C}=Ab({strategy:"fixed",placement:R,whileElementsMounted:(...J)=>mb(...J,{animationFrame:m==="always"}),elements:{reference:y.anchor},middleware:[Eb({mainAxis:s+E,alignmentAxis:i}),c&&Rb({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?Mb():void 0,...B}),c&&Db({...B}),Nb({...B,apply:({elements:J,rects:At,availableWidth:Zd,availableHeight:Jd})=>{const{width:Qd,height:ef}=At.reference,sn=J.floating.style;sn.setProperty("--radix-popper-available-width",`${Zd}px`),sn.setProperty("--radix-popper-available-height",`${Jd}px`),sn.setProperty("--radix-popper-anchor-width",`${Qd}px`),sn.setProperty("--radix-popper-anchor-height",`${ef}px`)}}),S&&Ib({element:S,padding:a}),Ub({arrowWidth:P,arrowHeight:E}),p&&jb({strategy:"referenceHidden",...B})]}),[A,I]=ru(F),$=Re(v);Ve(()=>{D&&$?.()},[D,$]);const K=C.arrow?.x,Ke=C.arrow?.y,Tt=C.arrow?.centerOffset!==0,[rn,He]=f.useState();return Ve(()=>{x&&He(window.getComputedStyle(x).zIndex)},[x]),d.jsx("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:D?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:rn,"--radix-popper-transform-origin":[C.transformOrigin?.x,C.transformOrigin?.y].join(" "),...C.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:d.jsx(Vb,{scope:n,placedSide:A,onArrowChange:T,arrowX:K,arrowY:Ke,shouldHideArrow:Tt,children:d.jsx(G.div,{"data-side":A,"data-align":I,...g,ref:w,style:{...g.style,animation:D?void 0:"none"}})})})});eu.displayName=ho;var tu="PopperArrow",Bb={top:"bottom",right:"left",bottom:"top",left:"right"},nu=f.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,o=Fb(tu,r),i=Bb[o.placedSide];return d.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:d.jsx(Ob,{...s,ref:n,style:{...s.style,display:"block"}})})});nu.displayName=tu;function $b(e){return e!==null}var Ub=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:s}=t,i=s.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[u,l]=ru(n),h={start:"0%",center:"50%",end:"100%"}[l],p=(s.arrow?.x??0)+a/2,m=(s.arrow?.y??0)+c/2;let v="",g="";return u==="bottom"?(v=i?h:`${p}px`,g=`${-c}px`):u==="top"?(v=i?h:`${p}px`,g=`${r.floating.height+c}px`):u==="right"?(v=`${-c}px`,g=i?h:`${m}px`):u==="left"&&(v=`${r.floating.width+c}px`,g=i?h:`${m}px`),{data:{x:v,y:g}}}});function ru(e){const[t,n="center"]=e.split("-");return[t,n]}var su=Zl,ou=Ql,iu=eu,au=nu,Wb=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),zb="VisuallyHidden",cu=f.forwardRef((e,t)=>d.jsx(G.span,{...e,ref:t,style:{...Wb,...e.style}}));cu.displayName=zb;var Kb=cu,[Zn,sS]=We("Tooltip",[qn]),Jn=qn(),lu="TooltipProvider",Hb=700,rs="tooltip.open",[Gb,po]=Zn(lu),uu=e=>{const{__scopeTooltip:t,delayDuration:n=Hb,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:o}=e,i=f.useRef(!0),a=f.useRef(!1),c=f.useRef(0);return f.useEffect(()=>{const u=c.current;return()=>window.clearTimeout(u)},[]),d.jsx(Gb,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:f.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:f.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:f.useCallback(u=>{a.current=u},[]),disableHoverableContent:s,children:o})};uu.displayName=lu;var zt="Tooltip",[Yb,Qn]=Zn(zt),du=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:s,onOpenChange:o,disableHoverableContent:i,delayDuration:a}=e,c=po(zt,e.__scopeTooltip),u=Jn(t),[l,h]=f.useState(null),p=Qe(),m=f.useRef(0),v=i??c.disableHoverableContent,g=a??c.delayDuration,y=f.useRef(!1),[x,b]=Wn({prop:r,defaultProp:s??!1,onChange:P=>{P?(c.onOpen(),document.dispatchEvent(new CustomEvent(rs))):c.onClose(),o?.(P)},caller:zt}),w=f.useMemo(()=>x?y.current?"delayed-open":"instant-open":"closed",[x]),S=f.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,b(!0)},[b]),T=f.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),M=f.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,b(!0),m.current=0},g)},[g,b]);return f.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),d.jsx(su,{...u,children:d.jsx(Yb,{scope:t,contentId:p,open:x,stateAttribute:w,trigger:l,onTriggerChange:h,onTriggerEnter:f.useCallback(()=>{c.isOpenDelayedRef.current?M():S()},[c.isOpenDelayedRef,M,S]),onTriggerLeave:f.useCallback(()=>{v?T():(window.clearTimeout(m.current),m.current=0)},[T,v]),onOpen:S,onClose:T,disableHoverableContent:v,children:n})})};du.displayName=zt;var ss="TooltipTrigger",fu=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=Qn(ss,n),o=po(ss,n),i=Jn(n),a=f.useRef(null),c=X(t,a,s.onTriggerChange),u=f.useRef(!1),l=f.useRef(!1),h=f.useCallback(()=>u.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),d.jsx(ou,{asChild:!0,...i,children:d.jsx(G.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:c,onPointerMove:_(e.onPointerMove,p=>{p.pointerType!=="touch"&&!l.current&&!o.isPointerInTransitRef.current&&(s.onTriggerEnter(),l.current=!0)}),onPointerLeave:_(e.onPointerLeave,()=>{s.onTriggerLeave(),l.current=!1}),onPointerDown:_(e.onPointerDown,()=>{s.open&&s.onClose(),u.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:_(e.onFocus,()=>{u.current||s.onOpen()}),onBlur:_(e.onBlur,s.onClose),onClick:_(e.onClick,s.onClose)})})});fu.displayName=ss;var Xb="TooltipPortal",[oS,qb]=Zn(Xb,{forceMount:void 0}),yt="TooltipContent",hu=f.forwardRef((e,t)=>{const n=qb(yt,e.__scopeTooltip),{forceMount:r=n.forceMount,side:s="top",...o}=e,i=Qn(yt,e.__scopeTooltip);return d.jsx(Ie,{present:r||i.open,children:i.disableHoverableContent?d.jsx(pu,{side:s,...o,ref:t}):d.jsx(Zb,{side:s,...o,ref:t})})}),Zb=f.forwardRef((e,t)=>{const n=Qn(yt,e.__scopeTooltip),r=po(yt,e.__scopeTooltip),s=f.useRef(null),o=X(t,s),[i,a]=f.useState(null),{trigger:c,onClose:u}=n,l=s.current,{onPointerInTransitChange:h}=r,p=f.useCallback(()=>{a(null),h(!1)},[h]),m=f.useCallback((v,g)=>{const y=v.currentTarget,x={x:v.clientX,y:v.clientY},b=nw(x,y.getBoundingClientRect()),w=rw(x,b),S=sw(g.getBoundingClientRect()),T=iw([...w,...S]);a(T),h(!0)},[h]);return f.useEffect(()=>()=>p(),[p]),f.useEffect(()=>{if(c&&l){const v=y=>m(y,l),g=y=>m(y,c);return c.addEventListener("pointerleave",v),l.addEventListener("pointerleave",g),()=>{c.removeEventListener("pointerleave",v),l.removeEventListener("pointerleave",g)}}},[c,l,m,p]),f.useEffect(()=>{if(i){const v=g=>{const y=g.target,x={x:g.clientX,y:g.clientY},b=c?.contains(y)||l?.contains(y),w=!ow(x,i);b?p():w&&(p(),u())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[c,l,i,u,p]),d.jsx(pu,{...e,ref:o})}),[Jb,Qb]=Zn(zt,{isInside:!1}),ew=va("TooltipContent"),pu=f.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=e,c=Qn(yt,n),u=Jn(n),{onClose:l}=c;return f.useEffect(()=>(document.addEventListener(rs,l),()=>document.removeEventListener(rs,l)),[l]),f.useEffect(()=>{if(c.trigger){const h=p=>{p.target?.contains(c.trigger)&&l()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[c.trigger,l]),d.jsx(zn,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:l,children:d.jsxs(iu,{"data-state":c.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[d.jsx(ew,{children:r}),d.jsx(Jb,{scope:n,isInside:!0,children:d.jsx(Kb,{id:c.contentId,role:"tooltip",children:s||r})})]})})});hu.displayName=yt;var mu="TooltipArrow",tw=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=Jn(n);return Qb(mu,n).isInside?null:d.jsx(au,{...s,...r,ref:t})});tw.displayName=mu;function nw(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,s,o)){case o:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function rw(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function sw(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function ow(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o],c=t[i],u=a.x,l=a.y,h=c.x,p=c.y;l>r!=p>r&&n<(h-u)*(r-l)/(p-l)+u&&(s=!s)}return s}function iw(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),aw(t)}function aw(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var cw=uu,lw=du,uw=fu,gu=hu;const dw=cw,fw=lw,hw=uw,vu=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>d.jsx(gu,{ref:r,sideOffset:t,className:j("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));vu.displayName=gu.displayName;const pw="sidebar_state",mw=3600*24*7,gw="20rem",vw="22rem",yw="3rem",xw="b",yu=f.createContext(null);function er(){const e=f.useContext(yu);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const xu=f.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:s,children:o,...i},a)=>{const c=Jv(),[u,l]=f.useState(!1),[h,p]=f.useState(e),m=t??h,v=f.useCallback(b=>{const w=typeof b=="function"?b(m):b;n?n(w):p(w),document.cookie=`${pw}=${w}; path=/; max-age=${mw}`},[n,m]),g=f.useCallback(()=>c?l(b=>!b):v(b=>!b),[c,v,l]);f.useEffect(()=>{const b=w=>{w.key===xw&&(w.metaKey||w.ctrlKey)&&(w.preventDefault(),g())};return window.addEventListener("keydown",b),()=>window.removeEventListener("keydown",b)},[g]);const y=m?"expanded":"collapsed",x=f.useMemo(()=>({state:y,open:m,setOpen:v,isMobile:c,openMobile:u,setOpenMobile:l,toggleSidebar:g}),[y,m,v,c,u,l,g]);return d.jsx(yu.Provider,{value:x,children:d.jsx(dw,{delayDuration:0,children:d.jsx("div",{style:{"--sidebar-width":gw,"--sidebar-width-icon":yw,...s},className:j("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:a,...i,children:o})})})});xu.displayName="SidebarProvider";const bw=f.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:s,...o},i)=>{const{isMobile:a,state:c,openMobile:u,setOpenMobile:l}=er();return n==="none"?d.jsx("div",{className:j("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...o,children:s}):a?d.jsx(xx,{open:u,onOpenChange:l,...o,children:d.jsx(_l,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":vw},side:e,children:d.jsx("div",{className:"flex h-full w-full flex-col",children:s})})}):d.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,children:[d.jsx("div",{className:j("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),d.jsx("div",{className:j("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...o,children:d.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:s})})]})});bw.displayName="Sidebar";const ww=f.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:s}=er();return d.jsxs(oe,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:j("h-7 w-7",e),onClick:o=>{t?.(o),s()},...n,children:[d.jsx(Ff,{}),d.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});ww.displayName="SidebarTrigger";const Cw=f.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=er();return d.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:j("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});Cw.displayName="SidebarRail";const Sw=f.forwardRef(({className:e,...t},n)=>d.jsx("main",{ref:n,className:j("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Sw.displayName="SidebarInset";const Tw=f.forwardRef(({className:e,...t},n)=>d.jsx(Un,{ref:n,"data-sidebar":"input",className:j("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Tw.displayName="SidebarInput";const Aw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"header",className:j("flex flex-col gap-2 p-2",e),...t}));Aw.displayName="SidebarHeader";const Pw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"footer",className:j("flex flex-col gap-2 p-2",e),...t}));Pw.displayName="SidebarFooter";const Ew=f.forwardRef(({className:e,...t},n)=>d.jsx(af,{ref:n,"data-sidebar":"separator",className:j("mx-2 w-auto bg-sidebar-border",e),...t}));Ew.displayName="SidebarSeparator";const Rw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"content",className:j("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));Rw.displayName="SidebarContent";const Mw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"group",className:j("relative flex w-full min-w-0 flex-col p-2",e),...t}));Mw.displayName="SidebarGroup";const Dw=f.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const s=t?Gt:"div";return d.jsx(s,{ref:r,"data-sidebar":"group-label",className:j("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});Dw.displayName="SidebarGroupLabel";const Nw=f.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const s=t?Gt:"button";return d.jsx(s,{ref:r,"data-sidebar":"group-action",className:j("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});Nw.displayName="SidebarGroupAction";const jw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"group-content",className:j("w-full text-sm",e),...t}));jw.displayName="SidebarGroupContent";const Iw=f.forwardRef(({className:e,...t},n)=>d.jsx("ul",{ref:n,"data-sidebar":"menu",className:j("flex w-full min-w-0 flex-col gap-1",e),...t}));Iw.displayName="SidebarMenu";const kw=f.forwardRef(({className:e,...t},n)=>d.jsx("li",{ref:n,"data-sidebar":"menu-item",className:j("group/menu-item relative",e),...t}));kw.displayName="SidebarMenuItem";const Ow=us("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!size-8"}},defaultVariants:{variant:"default",size:"default"}}),_w=f.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:s,className:o,...i},a)=>{const c=e?Gt:"button",{isMobile:u,state:l}=er(),h=d.jsx(c,{ref:a,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:j(Ow({variant:n,size:r}),o),...i});return s?(typeof s=="string"&&(s={children:s}),d.jsxs(fw,{children:[d.jsx(hw,{asChild:!0,children:h}),d.jsx(vu,{side:"right",align:"center",hidden:l!=="collapsed"||u,...s})]})):h});_w.displayName="SidebarMenuButton";const Lw=f.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},s)=>{const o=t?Gt:"button";return d.jsx(o,{ref:s,"data-sidebar":"menu-action",className:j("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});Lw.displayName="SidebarMenuAction";const Vw=f.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:j("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));Vw.displayName="SidebarMenuBadge";const Fw=f.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const s=f.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return d.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:j("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&d.jsx(Zi,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),d.jsx(Zi,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":s}})]})});Fw.displayName="SidebarMenuSkeleton";const Bw=f.forwardRef(({className:e,...t},n)=>d.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:j("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));Bw.displayName="SidebarMenuSub";const $w=f.forwardRef(({...e},t)=>d.jsx("li",{ref:t,...e}));$w.displayName="SidebarMenuSubItem";const Uw=f.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...s},o)=>{const i=e?Gt:"a";return d.jsx(i,{ref:o,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:j("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...s})});Uw.displayName="SidebarMenuSubButton";var bu="AlertDialog",[Ww,iS]=We(bu,[vl]),ke=vl(),wu=e=>{const{__scopeAlertDialog:t,...n}=e,r=ke(t);return d.jsx(Il,{...r,...n,modal:!0})};wu.displayName=bu;var zw="AlertDialogTrigger",Cu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=ke(n);return d.jsx(yx,{...s,...r,ref:t})});Cu.displayName=zw;var Kw="AlertDialogPortal",Su=e=>{const{__scopeAlertDialog:t,...n}=e,r=ke(t);return d.jsx(kl,{...r,...n})};Su.displayName=Kw;var Hw="AlertDialogOverlay",Tu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=ke(n);return d.jsx(Qs,{...s,...r,ref:t})});Tu.displayName=Hw;var pt="AlertDialogContent",[Gw,Yw]=Ww(pt),Xw=va("AlertDialogContent"),Au=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...s}=e,o=ke(n),i=f.useRef(null),a=X(t,i),c=f.useRef(null);return d.jsx(px,{contentName:pt,titleName:Pu,docsSlug:"alert-dialog",children:d.jsx(Gw,{scope:n,cancelRef:c,children:d.jsxs(eo,{role:"alertdialog",...o,...s,ref:a,onOpenAutoFocus:_(s.onOpenAutoFocus,u=>{u.preventDefault(),c.current?.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault(),children:[d.jsx(Xw,{children:r}),d.jsx(Zw,{contentRef:i})]})})})});Au.displayName=pt;var Pu="AlertDialogTitle",Eu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=ke(n);return d.jsx(to,{...s,...r,ref:t})});Eu.displayName=Pu;var Ru="AlertDialogDescription",Mu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=ke(n);return d.jsx(no,{...s,...r,ref:t})});Mu.displayName=Ru;var qw="AlertDialogAction",Du=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=ke(n);return d.jsx(ro,{...s,...r,ref:t})});Du.displayName=qw;var Nu="AlertDialogCancel",ju=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:s}=Yw(Nu,n),o=ke(n),i=X(t,s);return d.jsx(ro,{...o,...r,ref:i})});ju.displayName=Nu;var Zw=({contentRef:e})=>{const t=`\`${pt}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${pt}\` by passing a \`${Ru}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${pt}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return f.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},Jw=wu,Qw=Cu,e0=Su,Iu=Tu,ku=Au,Ou=Du,_u=ju,Lu=Eu,Vu=Mu;const t0=Jw,n0=Qw,r0=e0,Fu=f.forwardRef(({className:e,...t},n)=>d.jsx(Iu,{className:j("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));Fu.displayName=Iu.displayName;const Bu=f.forwardRef(({className:e,...t},n)=>d.jsxs(r0,{children:[d.jsx(Fu,{}),d.jsx(ku,{ref:n,className:j("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));Bu.displayName=ku.displayName;const $u=({className:e,...t})=>d.jsx("div",{className:j("flex flex-col space-y-2 text-center sm:text-left",e),...t});$u.displayName="AlertDialogHeader";const Uu=({className:e,...t})=>d.jsx("div",{className:j("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Uu.displayName="AlertDialogFooter";const Wu=f.forwardRef(({className:e,...t},n)=>d.jsx(Lu,{ref:n,className:j("text-lg font-semibold",e),...t}));Wu.displayName=Lu.displayName;const zu=f.forwardRef(({className:e,...t},n)=>d.jsx(Vu,{ref:n,className:j("text-sm text-muted-foreground",e),...t}));zu.displayName=Vu.displayName;const Ku=f.forwardRef(({className:e,...t},n)=>d.jsx(Ou,{ref:n,className:j(ya(),e),...t}));Ku.displayName=Ou.displayName;const Hu=f.forwardRef(({className:e,...t},n)=>d.jsx(_u,{ref:n,className:j(ya({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));Hu.displayName=_u.displayName;function s0({children:e,isCollapsed:t,onToggle:n}){const{conversations:r,currentConversationId:s,createConversation:o,deleteConversation:i,updateConversationTitle:a,setCurrentConversation:c,loadUserConversations:u,error:l}=nl(),{user:h}=xe(),[p,m]=f.useState(null),[v,g]=f.useState(""),[y,x]=f.useState(!1),[b,w]=f.useState(!1),[S,T]=f.useState(!1),[M,P]=f.useState(new Set),E=t!==void 0?t:y,R=async()=>{try{await o(),window.location.reload()}catch(D){console.error("创建对话失败:",D)}},N=async D=>{P(C=>new Set(C).add(D));try{await i(D),P(C=>{const A=new Set(C);return A.delete(D),A})}catch(C){console.error("删除对话失败:",C),P(A=>{const I=new Set(A);return I.delete(D),I})}},O=(D,C,A)=>{A.stopPropagation(),m(D),g(C)},V=async D=>{if(v.trim())try{await a(D,v.trim())}catch(C){console.error("更新标题失败:",C)}m(null),g("")},B=()=>{m(null),g("")},L=()=>{E?(n?n():x(!1),setTimeout(()=>{w(!1)},150)):(w(!0),setTimeout(()=>{n?n():x(!0)},150))};f.useEffect(()=>{E?w(!0):setTimeout(()=>{w(!1)},300)},[E]),f.useEffect(()=>{const D=()=>{T(window.innerWidth<768)};return D(),window.addEventListener("resize",D),()=>window.removeEventListener("resize",D)},[]),f.useEffect(()=>{S&&!y&&t===void 0&&x(!0)},[S]),f.useEffect(()=>{h&&u()},[h,u]),f.useEffect(()=>{const D=C=>{C.key==="b"&&(C.ctrlKey||C.metaKey)&&(C.preventDefault(),L())};return window.addEventListener("keydown",D),()=>window.removeEventListener("keydown",D)},[E]);const W=()=>{S&&!E&&L()},F=D=>{const C=D instanceof Date?D:new Date(D);if(isNaN(C.getTime()))return"无效日期";const I=new Date().getTime()-C.getTime(),$=Math.floor(I/(1e3*60*60*24));return $===0?"今天":$===1?"昨天":$<7?`${$}天前`:C.toLocaleDateString("zh-CN")};return d.jsxs(xu,{defaultOpen:!0,children:[S&&!E&&d.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:W}),S&&d.jsx(oe,{variant:"outline",size:"sm",className:"fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0",onClick:L,"aria-label":E?"展开侧边栏":"收起侧边栏",children:d.jsx(Nf,{className:"h-4 w-4"})}),d.jsxs("div",{className:j("flex min-h-screen w-full",S?"relative":""),children:[d.jsx("div",{className:j("bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",!S&&"flex-shrink-0",!S&&(E?"w-0 overflow-hidden":"w-80"),S&&"fixed top-0 left-0 h-full",S&&(E?"-translate-x-full":"translate-x-0 w-80")),children:d.jsxs("div",{className:"h-full flex flex-col",children:[d.jsxs("div",{className:j("border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",b?"opacity-0":"opacity-100"),children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("h2",{className:"text-lg font-semibold",children:"对话历史"}),d.jsx(oe,{onClick:R,size:"sm",variant:"outline",className:"h-8 w-8 p-0",disabled:!h,children:d.jsx(Wf,{className:"h-4 w-4"})})]}),l&&d.jsx("div",{className:"mt-2 text-xs text-red-500 bg-red-50 p-2 rounded",children:l})]}),d.jsx("div",{className:j("flex-1 overflow-auto transition-opacity duration-150 ease-in-out",b?"opacity-0":"opacity-100"),children:d.jsx("div",{className:"p-2",children:h?r.length===0?d.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"暂无对话历史"}):d.jsx("div",{className:"space-y-1",children:r.filter(D=>!M.has(D.id)).map(D=>d.jsxs("div",{className:j("group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",s===D.id&&"bg-accent"),onClick:()=>c(D.id),children:[d.jsx(If,{className:"h-4 w-4 flex-shrink-0"}),d.jsx("div",{className:"flex-1 min-w-0",children:p===D.id?d.jsxs("div",{className:"flex items-center gap-1",onClick:C=>C.stopPropagation(),children:[d.jsx(Un,{value:v,onChange:C=>g(C.target.value),className:"h-6 text-xs",onKeyDown:C=>{C.key==="Enter"?V(D.id):C.key==="Escape"&&B()},autoFocus:!0}),d.jsx(oe,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>V(D.id),children:d.jsx(ba,{className:"h-3 w-3"})}),d.jsx(oe,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:B,children:d.jsx(Ln,{className:"h-3 w-3"})})]}):d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"font-medium truncate",children:D.title}),d.jsxs("div",{className:"text-xs text-muted-foreground",children:[F(D.updatedAt)," •"," ",D.messages.length," 条消息"]})]})}),p!==D.id&&d.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[d.jsx(oe,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:C=>O(D.id,D.title,C),children:d.jsx($f,{className:"h-3 w-3"})}),d.jsxs(t0,{children:[d.jsx(n0,{asChild:!0,children:d.jsx(oe,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0 text-destructive hover:text-destructive",onClick:C=>C.stopPropagation(),children:d.jsx(Kf,{className:"h-3 w-3"})})}),d.jsxs(Bu,{children:[d.jsxs($u,{children:[d.jsx(Wu,{children:"确认删除对话"}),d.jsxs(zu,{children:['确定要删除对话 "',D.title,'" 吗？此操作不可撤销，对话中的所有消息都将被永久删除。']})]}),d.jsxs(Uu,{children:[d.jsx(Hu,{children:"取消"}),d.jsx(Ku,{onClick:()=>N(D.id),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"删除"})]})]})]})]})]},D.id))}):d.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"请先登录"})})}),d.jsx("div",{className:j("border-t px-4 py-3 transition-opacity duration-150 ease-in-out",b?"opacity-0":"opacity-100"),children:d.jsxs("div",{className:"text-xs text-muted-foreground",children:["共 ",r.length," 个对话"]})})]})}),d.jsx("main",{className:j("flex-1 flex flex-col min-w-0 w-full relative",S&&"w-full"),children:e})]})]})}function Gu(e){const t=e+"CollectionProvider",[n,r]=We(t),[s,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:y,children:x}=g,b=ye.useRef(null),w=ye.useRef(new Map).current;return d.jsx(s,{scope:y,itemMap:w,collectionRef:b,children:x})};i.displayName=t;const a=e+"CollectionSlot",c=Sn(a),u=ye.forwardRef((g,y)=>{const{scope:x,children:b}=g,w=o(a,x),S=X(y,w.collectionRef);return d.jsx(c,{ref:S,children:b})});u.displayName=a;const l=e+"CollectionItemSlot",h="data-radix-collection-item",p=Sn(l),m=ye.forwardRef((g,y)=>{const{scope:x,children:b,...w}=g,S=ye.useRef(null),T=X(y,S),M=o(l,x);return ye.useEffect(()=>(M.itemMap.set(S,{ref:S,...w}),()=>void M.itemMap.delete(S))),d.jsx(p,{[h]:"",ref:T,children:b})});m.displayName=l;function v(g){const y=o(e+"CollectionConsumer",g);return ye.useCallback(()=>{const b=y.collectionRef.current;if(!b)return[];const w=Array.from(b.querySelectorAll(`[${h}]`));return Array.from(y.itemMap.values()).sort((M,P)=>w.indexOf(M.ref.current)-w.indexOf(P.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:i,Slot:u,ItemSlot:m},v,r]}var o0=f.createContext(void 0);function Yu(e){const t=f.useContext(o0);return e||t||"ltr"}var Mr="rovingFocusGroup.onEntryFocus",i0={bubbles:!1,cancelable:!0},en="RovingFocusGroup",[os,Xu,a0]=Gu(en),[c0,qu]=We(en,[a0]),[l0,u0]=c0(en),Zu=f.forwardRef((e,t)=>d.jsx(os.Provider,{scope:e.__scopeRovingFocusGroup,children:d.jsx(os.Slot,{scope:e.__scopeRovingFocusGroup,children:d.jsx(d0,{...e,ref:t})})}));Zu.displayName=en;var d0=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:s=!1,dir:o,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:c,onEntryFocus:u,preventScrollOnEntryFocus:l=!1,...h}=e,p=f.useRef(null),m=X(t,p),v=Yu(o),[g,y]=Wn({prop:i,defaultProp:a??null,onChange:c,caller:en}),[x,b]=f.useState(!1),w=Re(u),S=Xu(n),T=f.useRef(!1),[M,P]=f.useState(0);return f.useEffect(()=>{const E=p.current;if(E)return E.addEventListener(Mr,w),()=>E.removeEventListener(Mr,w)},[w]),d.jsx(l0,{scope:n,orientation:r,dir:v,loop:s,currentTabStopId:g,onItemFocus:f.useCallback(E=>y(E),[y]),onItemShiftTab:f.useCallback(()=>b(!0),[]),onFocusableItemAdd:f.useCallback(()=>P(E=>E+1),[]),onFocusableItemRemove:f.useCallback(()=>P(E=>E-1),[]),children:d.jsx(G.div,{tabIndex:x||M===0?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:_(e.onMouseDown,()=>{T.current=!0}),onFocus:_(e.onFocus,E=>{const R=!T.current;if(E.target===E.currentTarget&&R&&!x){const N=new CustomEvent(Mr,i0);if(E.currentTarget.dispatchEvent(N),!N.defaultPrevented){const O=S().filter(F=>F.focusable),V=O.find(F=>F.active),B=O.find(F=>F.id===g),W=[V,B,...O].filter(Boolean).map(F=>F.ref.current);ed(W,l)}}T.current=!1}),onBlur:_(e.onBlur,()=>b(!1))})})}),Ju="RovingFocusGroupItem",Qu=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:s=!1,tabStopId:o,children:i,...a}=e,c=Qe(),u=o||c,l=u0(Ju,n),h=l.currentTabStopId===u,p=Xu(n),{onFocusableItemAdd:m,onFocusableItemRemove:v,currentTabStopId:g}=l;return f.useEffect(()=>{if(r)return m(),()=>v()},[r,m,v]),d.jsx(os.ItemSlot,{scope:n,id:u,focusable:r,active:s,children:d.jsx(G.span,{tabIndex:h?0:-1,"data-orientation":l.orientation,...a,ref:t,onMouseDown:_(e.onMouseDown,y=>{r?l.onItemFocus(u):y.preventDefault()}),onFocus:_(e.onFocus,()=>l.onItemFocus(u)),onKeyDown:_(e.onKeyDown,y=>{if(y.key==="Tab"&&y.shiftKey){l.onItemShiftTab();return}if(y.target!==y.currentTarget)return;const x=p0(y,l.orientation,l.dir);if(x!==void 0){if(y.metaKey||y.ctrlKey||y.altKey||y.shiftKey)return;y.preventDefault();let w=p().filter(S=>S.focusable).map(S=>S.ref.current);if(x==="last")w.reverse();else if(x==="prev"||x==="next"){x==="prev"&&w.reverse();const S=w.indexOf(y.currentTarget);w=l.loop?m0(w,S+1):w.slice(S+1)}setTimeout(()=>ed(w))}}),children:typeof i=="function"?i({isCurrentTabStop:h,hasTabStop:g!=null}):i})})});Qu.displayName=Ju;var f0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function h0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function p0(e,t,n){const r=h0(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return f0[r]}function ed(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function m0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var g0=Zu,v0=Qu,is=["Enter"," "],y0=["ArrowDown","PageUp","Home"],td=["ArrowUp","PageDown","End"],x0=[...y0,...td],b0={ltr:[...is,"ArrowRight"],rtl:[...is,"ArrowLeft"]},w0={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tn="Menu",[Kt,C0,S0]=Gu(tn),[nt,nd]=We(tn,[S0,qn,qu]),tr=qn(),rd=qu(),[T0,rt]=nt(tn),[A0,nn]=nt(tn),sd=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:s,onOpenChange:o,modal:i=!0}=e,a=tr(t),[c,u]=f.useState(null),l=f.useRef(!1),h=Re(o),p=Yu(s);return f.useEffect(()=>{const m=()=>{l.current=!0,document.addEventListener("pointerdown",v,{capture:!0,once:!0}),document.addEventListener("pointermove",v,{capture:!0,once:!0})},v=()=>l.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",v,{capture:!0}),document.removeEventListener("pointermove",v,{capture:!0})}},[]),d.jsx(su,{...a,children:d.jsx(T0,{scope:t,open:n,onOpenChange:h,content:c,onContentChange:u,children:d.jsx(A0,{scope:t,onClose:f.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:l,dir:p,modal:i,children:r})})})};sd.displayName=tn;var P0="MenuAnchor",mo=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=tr(n);return d.jsx(ou,{...s,...r,ref:t})});mo.displayName=P0;var go="MenuPortal",[E0,od]=nt(go,{forceMount:void 0}),id=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:s}=e,o=rt(go,t);return d.jsx(E0,{scope:t,forceMount:n,children:d.jsx(Ie,{present:n||o.open,children:d.jsx(Ys,{asChild:!0,container:s,children:r})})})};id.displayName=go;var he="MenuContent",[R0,vo]=nt(he),ad=f.forwardRef((e,t)=>{const n=od(he,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,o=rt(he,e.__scopeMenu),i=nn(he,e.__scopeMenu);return d.jsx(Kt.Provider,{scope:e.__scopeMenu,children:d.jsx(Ie,{present:r||o.open,children:d.jsx(Kt.Slot,{scope:e.__scopeMenu,children:i.modal?d.jsx(M0,{...s,ref:t}):d.jsx(D0,{...s,ref:t})})})})}),M0=f.forwardRef((e,t)=>{const n=rt(he,e.__scopeMenu),r=f.useRef(null),s=X(t,r);return f.useEffect(()=>{const o=r.current;if(o)return ml(o)},[]),d.jsx(yo,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:_(e.onFocusOutside,o=>o.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),D0=f.forwardRef((e,t)=>{const n=rt(he,e.__scopeMenu);return d.jsx(yo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),N0=Sn("MenuContent.ScrollLock"),yo=f.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:c,onEscapeKeyDown:u,onPointerDownOutside:l,onFocusOutside:h,onInteractOutside:p,onDismiss:m,disableOutsideScroll:v,...g}=e,y=rt(he,n),x=nn(he,n),b=tr(n),w=rd(n),S=C0(n),[T,M]=f.useState(null),P=f.useRef(null),E=X(t,P,y.onContentChange),R=f.useRef(0),N=f.useRef(""),O=f.useRef(0),V=f.useRef(null),B=f.useRef("right"),L=f.useRef(0),W=v?Xs:f.Fragment,F=v?{as:N0,allowPinchZoom:!0}:void 0,D=A=>{const I=N.current+A,$=S().filter(J=>!J.disabled),K=document.activeElement,Ke=$.find(J=>J.ref.current===K)?.textValue,Tt=$.map(J=>J.textValue),rn=W0(Tt,I,Ke),He=$.find(J=>J.textValue===rn)?.ref.current;(function J(At){N.current=At,window.clearTimeout(R.current),At!==""&&(R.current=window.setTimeout(()=>J(""),1e3))})(I),He&&setTimeout(()=>He.focus())};f.useEffect(()=>()=>window.clearTimeout(R.current),[]),il();const C=f.useCallback(A=>B.current===V.current?.side&&K0(A,V.current?.area),[]);return d.jsx(R0,{scope:n,searchRef:N,onItemEnter:f.useCallback(A=>{C(A)&&A.preventDefault()},[C]),onItemLeave:f.useCallback(A=>{C(A)||(P.current?.focus(),M(null))},[C]),onTriggerLeave:f.useCallback(A=>{C(A)&&A.preventDefault()},[C]),pointerGraceTimerRef:O,onPointerGraceIntentChange:f.useCallback(A=>{V.current=A},[]),children:d.jsx(W,{...F,children:d.jsx(Gs,{asChild:!0,trapped:s,onMountAutoFocus:_(o,A=>{A.preventDefault(),P.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:d.jsx(zn,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:l,onFocusOutside:h,onInteractOutside:p,onDismiss:m,children:d.jsx(g0,{asChild:!0,...w,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:T,onCurrentTabStopIdChange:M,onEntryFocus:_(c,A=>{x.isUsingKeyboardRef.current||A.preventDefault()}),preventScrollOnEntryFocus:!0,children:d.jsx(iu,{role:"menu","aria-orientation":"vertical","data-state":Sd(y.open),"data-radix-menu-content":"",dir:x.dir,...b,...g,ref:E,style:{outline:"none",...g.style},onKeyDown:_(g.onKeyDown,A=>{const $=A.target.closest("[data-radix-menu-content]")===A.currentTarget,K=A.ctrlKey||A.altKey||A.metaKey,Ke=A.key.length===1;$&&(A.key==="Tab"&&A.preventDefault(),!K&&Ke&&D(A.key));const Tt=P.current;if(A.target!==Tt||!x0.includes(A.key))return;A.preventDefault();const He=S().filter(J=>!J.disabled).map(J=>J.ref.current);td.includes(A.key)&&He.reverse(),$0(He)}),onBlur:_(e.onBlur,A=>{A.currentTarget.contains(A.target)||(window.clearTimeout(R.current),N.current="")}),onPointerMove:_(e.onPointerMove,Ht(A=>{const I=A.target,$=L.current!==A.clientX;if(A.currentTarget.contains(I)&&$){const K=A.clientX>L.current?"right":"left";B.current=K,L.current=A.clientX}}))})})})})})})});ad.displayName=he;var j0="MenuGroup",xo=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(G.div,{role:"group",...r,ref:t})});xo.displayName=j0;var I0="MenuLabel",cd=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(G.div,{...r,ref:t})});cd.displayName=I0;var On="MenuItem",ca="menu.itemSelect",nr=f.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...s}=e,o=f.useRef(null),i=nn(On,e.__scopeMenu),a=vo(On,e.__scopeMenu),c=X(t,o),u=f.useRef(!1),l=()=>{const h=o.current;if(!n&&h){const p=new CustomEvent(ca,{bubbles:!0,cancelable:!0});h.addEventListener(ca,m=>r?.(m),{once:!0}),fa(h,p),p.defaultPrevented?u.current=!1:i.onClose()}};return d.jsx(ld,{...s,ref:c,disabled:n,onClick:_(e.onClick,l),onPointerDown:h=>{e.onPointerDown?.(h),u.current=!0},onPointerUp:_(e.onPointerUp,h=>{u.current||h.currentTarget?.click()}),onKeyDown:_(e.onKeyDown,h=>{const p=a.searchRef.current!=="";n||p&&h.key===" "||is.includes(h.key)&&(h.currentTarget.click(),h.preventDefault())})})});nr.displayName=On;var ld=f.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:s,...o}=e,i=vo(On,n),a=rd(n),c=f.useRef(null),u=X(t,c),[l,h]=f.useState(!1),[p,m]=f.useState("");return f.useEffect(()=>{const v=c.current;v&&m((v.textContent??"").trim())},[o.children]),d.jsx(Kt.ItemSlot,{scope:n,disabled:r,textValue:s??p,children:d.jsx(v0,{asChild:!0,...a,focusable:!r,children:d.jsx(G.div,{role:"menuitem","data-highlighted":l?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...o,ref:u,onPointerMove:_(e.onPointerMove,Ht(v=>{r?i.onItemLeave(v):(i.onItemEnter(v),v.defaultPrevented||v.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:_(e.onPointerLeave,Ht(v=>i.onItemLeave(v))),onFocus:_(e.onFocus,()=>h(!0)),onBlur:_(e.onBlur,()=>h(!1))})})})}),k0="MenuCheckboxItem",ud=f.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...s}=e;return d.jsx(md,{scope:e.__scopeMenu,checked:n,children:d.jsx(nr,{role:"menuitemcheckbox","aria-checked":_n(n)?"mixed":n,...s,ref:t,"data-state":wo(n),onSelect:_(s.onSelect,()=>r?.(_n(n)?!0:!n),{checkForDefaultPrevented:!1})})})});ud.displayName=k0;var dd="MenuRadioGroup",[O0,_0]=nt(dd,{value:void 0,onValueChange:()=>{}}),fd=f.forwardRef((e,t)=>{const{value:n,onValueChange:r,...s}=e,o=Re(r);return d.jsx(O0,{scope:e.__scopeMenu,value:n,onValueChange:o,children:d.jsx(xo,{...s,ref:t})})});fd.displayName=dd;var hd="MenuRadioItem",pd=f.forwardRef((e,t)=>{const{value:n,...r}=e,s=_0(hd,e.__scopeMenu),o=n===s.value;return d.jsx(md,{scope:e.__scopeMenu,checked:o,children:d.jsx(nr,{role:"menuitemradio","aria-checked":o,...r,ref:t,"data-state":wo(o),onSelect:_(r.onSelect,()=>s.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});pd.displayName=hd;var bo="MenuItemIndicator",[md,L0]=nt(bo,{checked:!1}),gd=f.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...s}=e,o=L0(bo,n);return d.jsx(Ie,{present:r||_n(o.checked)||o.checked===!0,children:d.jsx(G.span,{...s,ref:t,"data-state":wo(o.checked)})})});gd.displayName=bo;var V0="MenuSeparator",vd=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return d.jsx(G.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});vd.displayName=V0;var F0="MenuArrow",yd=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=tr(n);return d.jsx(au,{...s,...r,ref:t})});yd.displayName=F0;var B0="MenuSub",[aS,xd]=nt(B0),Rt="MenuSubTrigger",bd=f.forwardRef((e,t)=>{const n=rt(Rt,e.__scopeMenu),r=nn(Rt,e.__scopeMenu),s=xd(Rt,e.__scopeMenu),o=vo(Rt,e.__scopeMenu),i=f.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:c}=o,u={__scopeMenu:e.__scopeMenu},l=f.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return f.useEffect(()=>l,[l]),f.useEffect(()=>{const h=a.current;return()=>{window.clearTimeout(h),c(null)}},[a,c]),d.jsx(mo,{asChild:!0,...u,children:d.jsx(ld,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":Sd(n.open),...e,ref:xa(t,s.onTriggerChange),onClick:h=>{e.onClick?.(h),!(e.disabled||h.defaultPrevented)&&(h.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:_(e.onPointerMove,Ht(h=>{o.onItemEnter(h),!h.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(o.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),l()},100))})),onPointerLeave:_(e.onPointerLeave,Ht(h=>{l();const p=n.content?.getBoundingClientRect();if(p){const m=n.content?.dataset.side,v=m==="right",g=v?-5:5,y=p[v?"left":"right"],x=p[v?"right":"left"];o.onPointerGraceIntentChange({area:[{x:h.clientX+g,y:h.clientY},{x:y,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:y,y:p.bottom}],side:m}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>o.onPointerGraceIntentChange(null),300)}else{if(o.onTriggerLeave(h),h.defaultPrevented)return;o.onPointerGraceIntentChange(null)}})),onKeyDown:_(e.onKeyDown,h=>{const p=o.searchRef.current!=="";e.disabled||p&&h.key===" "||b0[r.dir].includes(h.key)&&(n.onOpenChange(!0),n.content?.focus(),h.preventDefault())})})})});bd.displayName=Rt;var wd="MenuSubContent",Cd=f.forwardRef((e,t)=>{const n=od(he,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,o=rt(he,e.__scopeMenu),i=nn(he,e.__scopeMenu),a=xd(wd,e.__scopeMenu),c=f.useRef(null),u=X(t,c);return d.jsx(Kt.Provider,{scope:e.__scopeMenu,children:d.jsx(Ie,{present:r||o.open,children:d.jsx(Kt.Slot,{scope:e.__scopeMenu,children:d.jsx(yo,{id:a.contentId,"aria-labelledby":a.triggerId,...s,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:l=>{i.isUsingKeyboardRef.current&&c.current?.focus(),l.preventDefault()},onCloseAutoFocus:l=>l.preventDefault(),onFocusOutside:_(e.onFocusOutside,l=>{l.target!==a.trigger&&o.onOpenChange(!1)}),onEscapeKeyDown:_(e.onEscapeKeyDown,l=>{i.onClose(),l.preventDefault()}),onKeyDown:_(e.onKeyDown,l=>{const h=l.currentTarget.contains(l.target),p=w0[i.dir].includes(l.key);h&&p&&(o.onOpenChange(!1),a.trigger?.focus(),l.preventDefault())})})})})})});Cd.displayName=wd;function Sd(e){return e?"open":"closed"}function _n(e){return e==="indeterminate"}function wo(e){return _n(e)?"indeterminate":e?"checked":"unchecked"}function $0(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function U0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function W0(e,t,n){const s=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let i=U0(e,Math.max(o,0));s.length===1&&(i=i.filter(u=>u!==n));const c=i.find(u=>u.toLowerCase().startsWith(s.toLowerCase()));return c!==n?c:void 0}function z0(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o],c=t[i],u=a.x,l=a.y,h=c.x,p=c.y;l>r!=p>r&&n<(h-u)*(r-l)/(p-l)+u&&(s=!s)}return s}function K0(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return z0(n,t)}function Ht(e){return t=>t.pointerType==="mouse"?e(t):void 0}var H0=sd,G0=mo,Y0=id,X0=ad,q0=xo,Z0=cd,J0=nr,Q0=ud,eC=fd,tC=pd,nC=gd,rC=vd,sC=yd,oC=bd,iC=Cd,rr="DropdownMenu",[aC,cS]=We(rr,[nd]),re=nd(),[cC,Td]=aC(rr),Ad=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:s,defaultOpen:o,onOpenChange:i,modal:a=!0}=e,c=re(t),u=f.useRef(null),[l,h]=Wn({prop:s,defaultProp:o??!1,onChange:i,caller:rr});return d.jsx(cC,{scope:t,triggerId:Qe(),triggerRef:u,contentId:Qe(),open:l,onOpenChange:h,onOpenToggle:f.useCallback(()=>h(p=>!p),[h]),modal:a,children:d.jsx(H0,{...c,open:l,onOpenChange:h,dir:r,modal:a,children:n})})};Ad.displayName=rr;var Pd="DropdownMenuTrigger",Ed=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...s}=e,o=Td(Pd,n),i=re(n);return d.jsx(G0,{asChild:!0,...i,children:d.jsx(G.button,{type:"button",id:o.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":o.open?o.contentId:void 0,"data-state":o.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:xa(t,o.triggerRef),onPointerDown:_(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(o.onOpenToggle(),o.open||a.preventDefault())}),onKeyDown:_(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&o.onOpenToggle(),a.key==="ArrowDown"&&o.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});Ed.displayName=Pd;var lC="DropdownMenuPortal",Rd=e=>{const{__scopeDropdownMenu:t,...n}=e,r=re(t);return d.jsx(Y0,{...r,...n})};Rd.displayName=lC;var Md="DropdownMenuContent",Dd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=Td(Md,n),o=re(n),i=f.useRef(!1);return d.jsx(X0,{id:s.contentId,"aria-labelledby":s.triggerId,...o,...r,ref:t,onCloseAutoFocus:_(e.onCloseAutoFocus,a=>{i.current||s.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:_(e.onInteractOutside,a=>{const c=a.detail.originalEvent,u=c.button===0&&c.ctrlKey===!0,l=c.button===2||u;(!s.modal||l)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Dd.displayName=Md;var uC="DropdownMenuGroup",dC=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(q0,{...s,...r,ref:t})});dC.displayName=uC;var fC="DropdownMenuLabel",Nd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(Z0,{...s,...r,ref:t})});Nd.displayName=fC;var hC="DropdownMenuItem",jd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(J0,{...s,...r,ref:t})});jd.displayName=hC;var pC="DropdownMenuCheckboxItem",Id=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(Q0,{...s,...r,ref:t})});Id.displayName=pC;var mC="DropdownMenuRadioGroup",gC=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(eC,{...s,...r,ref:t})});gC.displayName=mC;var vC="DropdownMenuRadioItem",kd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(tC,{...s,...r,ref:t})});kd.displayName=vC;var yC="DropdownMenuItemIndicator",Od=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(nC,{...s,...r,ref:t})});Od.displayName=yC;var xC="DropdownMenuSeparator",_d=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(rC,{...s,...r,ref:t})});_d.displayName=xC;var bC="DropdownMenuArrow",wC=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(sC,{...s,...r,ref:t})});wC.displayName=bC;var CC="DropdownMenuSubTrigger",Ld=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(oC,{...s,...r,ref:t})});Ld.displayName=CC;var SC="DropdownMenuSubContent",Vd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=re(n);return d.jsx(iC,{...s,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Vd.displayName=SC;var TC=Ad,AC=Ed,PC=Rd,Fd=Dd,Bd=Nd,$d=jd,Ud=Id,Wd=kd,zd=Od,Kd=_d,Hd=Ld,Gd=Vd;const EC=TC,RC=AC,MC=f.forwardRef(({className:e,inset:t,children:n,...r},s)=>d.jsxs(Hd,{ref:s,className:j("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,d.jsx(Ca,{className:"ml-auto h-4 w-4"})]}));MC.displayName=Hd.displayName;const DC=f.forwardRef(({className:e,...t},n)=>d.jsx(Gd,{ref:n,className:j("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));DC.displayName=Gd.displayName;const Yd=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>d.jsx(PC,{children:d.jsx(Fd,{ref:r,sideOffset:t,className:j("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));Yd.displayName=Fd.displayName;const as=f.forwardRef(({className:e,inset:t,...n},r)=>d.jsx($d,{ref:r,className:j("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));as.displayName=$d.displayName;const NC=f.forwardRef(({className:e,children:t,checked:n,...r},s)=>d.jsxs(Ud,{ref:s,className:j("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[d.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:d.jsx(zd,{children:d.jsx(ba,{className:"h-4 w-4"})})}),t]}));NC.displayName=Ud.displayName;const jC=f.forwardRef(({className:e,children:t,...n},r)=>d.jsxs(Wd,{ref:r,className:j("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[d.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:d.jsx(zd,{children:d.jsx(wf,{className:"h-2 w-2 fill-current"})})}),t]}));jC.displayName=Wd.displayName;const Xd=f.forwardRef(({className:e,inset:t,...n},r)=>d.jsx(Bd,{ref:r,className:j("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));Xd.displayName=Bd.displayName;const cs=f.forwardRef(({className:e,...t},n)=>d.jsx(Kd,{ref:n,className:j("-mx-1 my-1 h-px bg-muted",e),...t}));cs.displayName=Kd.displayName;function IC({className:e}){const t=da(),{logout:n}=ga(),{user:r}=xe(),s=a=>a.split(" ").map(c=>c[0]).join("").toUpperCase().slice(0,2),o=()=>{t({to:"/user"})},i=()=>{n(),t({to:"/auth/login"})};return d.jsxs(EC,{children:[d.jsx(RC,{asChild:!0,children:d.jsx(oe,{variant:"ghost",size:"sm",className:`h-8 w-8 rounded-full p-0 ${e}`,children:d.jsxs(ha,{className:"h-8 w-8",children:[d.jsx(pa,{src:r?.avatar||"",alt:r?.name||"用户"}),d.jsx(ma,{className:"text-xs",children:s(r?.name||"用户")})]})})}),d.jsxs(Yd,{align:"end",className:"w-56",children:[d.jsx(Xd,{className:"font-normal",children:d.jsx("div",{className:"flex flex-col space-y-1",children:d.jsx("p",{className:"text-sm font-medium leading-none",children:r?.name||"用户"})})}),d.jsx(cs,{}),d.jsxs(as,{onClick:o,children:[d.jsx(hf,{className:"mr-2 h-4 w-4"}),d.jsx("span",{children:"账号管理"})]}),d.jsx(cs,{}),d.jsxs(as,{onClick:i,children:[d.jsx(Mf,{className:"mr-2 h-4 w-4"}),d.jsx("span",{children:"退出登录"})]})]})]})}function kC({references:e,messageId:t}){const[n,r]=f.useState(new Set),s=o=>{r(i=>{const a=new Set(i);return a.has(o)?a.delete(o):a.add(o),a})};return!e||e.length===0?null:d.jsxs("div",{className:"mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[d.jsx(Af,{className:"h-4 w-4 text-blue-600"}),d.jsx("span",{className:"text-sm font-medium text-blue-800",children:"参考文档"}),d.jsxs(pf,{variant:"secondary",className:"text-xs",children:[e.length," 个文档"]})]}),d.jsx("div",{className:"space-y-2",children:e.map((o,i)=>d.jsxs("div",{className:"border border-blue-200 rounded-md bg-white",children:[d.jsxs(oe,{variant:"ghost",className:"w-full justify-between p-3 h-auto text-left",onClick:()=>s(i),children:[d.jsxs("div",{className:"flex-1",children:[d.jsx("div",{className:"font-medium text-sm text-gray-900",children:o.doc_name}),o.page_number&&o.page_number.length>0&&d.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["页码: ",o.page_number.join(", ")]})]}),n.has(i)?d.jsx(wa,{className:"h-4 w-4 text-gray-500"}):d.jsx(Ca,{className:"h-4 w-4 text-gray-500"})]}),n.has(i)&&d.jsxs("div",{className:"px-3 pb-3 border-t border-gray-100",children:[o.title&&d.jsx("div",{className:"text-sm font-medium text-blue-700 mb-2",children:o.title}),d.jsx("div",{className:"text-sm text-gray-700 leading-relaxed",children:o.text}),o.doc_id&&d.jsxs("div",{className:"text-xs text-gray-400 mt-2",children:["文档ID: ",o.doc_id]})]})]},i))})]})}let ls=null,Mt=null;const qd=async()=>{if(typeof window>"u")throw new Error("File reading is only supported on the client side");ls||(ls=await Co(()=>import("./index-Dqv_FIK3.js").then(e=>e.i),__vite__mapDeps([0,1]))),Mt||(Mt=await Co(()=>import("./pdf-CtA8PhPd.js"),[]),Mt.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${Mt.version}/pdf.worker.min.js`)};async function OC(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=s=>{const o=s.target?.result;t(o)},r.onerror=()=>n(new Error("Failed to read text file")),r.readAsText(e,"utf-8")})}async function _C(e){return await qd(),new Promise((t,n)=>{const r=new FileReader;r.onload=async s=>{try{const o=s.target?.result,i=await ls.extractRawText({arrayBuffer:o});t(i.value)}catch(o){n(new Error(`Failed to read Word file: ${o}`))}},r.onerror=()=>n(new Error("Failed to read Word file")),r.readAsArrayBuffer(e)})}async function LC(e){return await qd(),new Promise((t,n)=>{const r=new FileReader;r.onload=async s=>{try{const o=s.target?.result,i=await Mt.getDocument({data:o}).promise;let a="";for(let c=1;c<=i.numPages;c++){const h=(await(await i.getPage(c)).getTextContent()).items.map(p=>p.str).join(" ");a+=`Page ${c}:
${h}

`}t(a)}catch(o){n(new Error(`Failed to read PDF file: ${o}`))}},r.onerror=()=>n(new Error("Failed to read PDF file")),r.readAsArrayBuffer(e)})}async function VC(e){const n=e.name.toLowerCase().split(".").pop();console.log(`Reading file: ${e.name} (${e.size} bytes)`),console.log(`File type: ${e.type}`),console.log(`File extension: ${n}`);try{let r="";if(n==="txt"||e.type==="text/plain")r=await OC(e);else if(n==="docx"||e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document")r=await _C(e);else if(n==="pdf"||e.type==="application/pdf")r=await LC(e);else throw new Error(`Unsupported file type: ${n}. Only TXT, DOCX, and PDF files are supported.`);return console.log(`Successfully read file content (${r.length} characters)`),console.log("File content preview:",r.substring(0,200)+"..."),r}catch(r){throw console.error("Error reading file:",r),r}}function FC(e){const n=e.name.toLowerCase().split(".").pop(),r=["txt","docx","pdf"],s=["text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/pdf"];return r.includes(n||"")||s.includes(e.type)}function BC(){return d.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[d.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:d.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),d.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:d.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),d.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:d.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function $C({variant:e="received",layout:t="default",className:n,children:r}){return d.jsx("div",{className:j("flex items-start gap-2 mb-4",e==="sent"&&"flex-row-reverse",n),children:r})}function UC({variant:e="received",isLoading:t,className:n,children:r}){return d.jsx("div",{className:j("rounded-lg p-3",e==="sent"?"bg-primary text-primary-foreground":"bg-muted",n),children:t?d.jsx("div",{className:"flex items-center space-x-2",children:d.jsx(BC,{})}):r})}function WC({src:e,fallback:t="AI",className:n}){return d.jsxs(ha,{className:j("h-8 w-8",n),children:[e&&d.jsx(pa,{src:e}),d.jsx(ma,{children:t})]})}function zC({icon:e,onClick:t,className:n}){return d.jsx(oe,{variant:"ghost",size:"icon",className:j("h-6 w-6",n),onClick:t,children:e})}function KC({className:e,children:t}){return d.jsx("div",{className:j("flex items-center gap-1 mt-2",e),children:t})}function HC({children:e,redirectTo:t="/auth/login"}){const{isAuthenticated:n,isLoading:r}=ga(),s=da();return ye.useEffect(()=>{if(!r&&!n){const o=window.location.pathname;o!==t&&localStorage.setItem("redirectAfterLogin",o),s({to:t})}},[r,n,s,t]),r?d.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Dr,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),d.jsx("p",{className:"text-gray-600",children:"正在验证登录状态..."})]})}):n?d.jsx(d.Fragment,{children:e}):d.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Dr,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),d.jsx("p",{className:"text-gray-600",children:"正在跳转到登录页面..."})]})})}const la=["知识库查询","方案改进","数据计算"];function GC(){const[e,t]=f.useState(la[0]),[n,r]=f.useState(!1),[s,o]=f.useState(null),{getCurrentConversation:i,addMessage:a,currentConversationId:c,createConversation:u,conversations:l}=nl(),p=i()?.messages||[],m=f.useCallback(async C=>{try{const A=`${C} - ${new Date().toLocaleString("zh-CN")}`;await u(A),t(C),o(null),console.log(`切换到 ${C} 模式，已创建新对话`)}catch(A){console.error("切换模式时创建新对话失败:",A),t(C)}},[u]);f.useEffect(()=>{if(!c&&l.length===0){const C=`${e} - ${new Date().toLocaleString("zh-CN")}`;u(C).catch(console.error)}},[c,l.length,u,e]);const v="sk-5ff58b88af7343b7bbb388079e1442f2",g="622fbd2ef57c413baafa29527d205414",y="38669993697942e6a8ac1a9f1aa591e0",x="680919cbac364a80b24306137e5debeb",[b,w]=f.useState(null),[S,T]=f.useState(""),[M,P]=f.useState([]),E=f.useCallback(C=>{w(C),T(""),P([])},[]),R=C=>{P(C),console.log("=== 格式化显示结果 ==="),C.forEach((A,I)=>{console.log(`%c问题 ${I+1}:`,"color: #e74c3c; font-weight: bold;"),console.log(`%c原文: ${A.origin}`,"color: #3498db;"),console.log(`%c问题描述: ${A.issueDes}`,"color: #f39c12;"),console.log(`%c改进建议: ${A.suggestion}`,"color: #27ae60;"),console.log(`%c依据: ${A.reason}`,"color: #9b59b6;"),console.log("---")})},N=async C=>{try{const{user:A}=xe.getState();if(A&&A.id){console.log("=== 数据库更新 ==="),console.log(`Token 使用量: ${C}`),console.log("请求次数: +1");let I;A.dingTalkUnionId?I=await df(A.dingTalkUnionId,C):I=await ff(A.id,C),I?(console.log("用户token使用量更新成功:",I),xe.getState().setUser(I)):console.error("更新用户token使用量失败")}else console.warn("用户信息不完整，无法更新数据库")}catch(A){console.error("更新数据库失败:",A)}},O=async C=>{try{const A=await fetch(`https://dashscope.aliyuncs.com/api/v1/apps/${y}/completion`,{method:"POST",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify({input:{prompt:C},parameters:{},debug:{}})});if(!A.ok)throw new Error(`API 请求失败: ${A.status} ${A.statusText}`);const I=await A.json();if(console.log("=== DashScope API 响应 ==="),console.log("完整响应数据:"),console.log(JSON.stringify(I,null,2)),I.output&&I.output.text)try{const $=I.output.text.replace(/```json\n?/,"").replace(/\n?```$/,""),K=JSON.parse($);if(console.log("=== 解析后的结果 ==="),console.log(K),R(K),I.usage&&I.usage.models&&I.usage.models[0]){const Ke=I.usage.models[0].input_tokens+I.usage.models[0].output_tokens;await N(Ke)}}catch($){console.error("解析 JSON 失败:",$),console.log("原始 text 内容:",I.output.text)}console.log("=== API 响应完成 ===")}catch(A){throw console.error("API 请求失败:",A),A}},V=()=>{r(!n)},B=async(C,A)=>{console.log("=== API调用调试信息 ==="),console.log("API_KEY:",v),console.log("APP_ID:",g),console.log("环境变量 VITE_DASHSCOPE_API_KEY:","sk-5ff58b88af7343b7bbb388079e1442f2"),console.log("环境变量 VITE_YOUR_APP_ID:","622fbd2ef57c413baafa29527d205414"),console.log("========================");const I=`https://dashscope.aliyuncs.com/api/v1/apps/${g}/completion`,$={input:{prompt:C},parameters:{},debug:{}};A&&($.input.session_id=A);const K=await fetch(I,{method:"POST",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify($)});if(!K.ok)throw new Error(`API请求失败: ${K.status} ${K.statusText}`);return await K.json()},L=async(C,A)=>{console.log("=== 计算API调用调试信息 ==="),console.log("API_KEY:",v),console.log("CALCULATE_APP_ID:",x),console.log("环境变量 VITE_CALCULATE_APP_ID:","680919cbac364a80b24306137e5debeb"),console.log("========================");const I=`https://dashscope.aliyuncs.com/api/v1/apps/${x}/completion`,$={input:{prompt:C},parameters:{},debug:{}};A&&($.input.session_id=A);const K=await fetch(I,{method:"POST",headers:{Authorization:`Bearer ${v}`,"Content-Type":"application/json"},body:JSON.stringify($)});if(!K.ok)throw new Error(`计算API请求失败: ${K.status} ${K.statusText}`);return await K.json()},W=async C=>{if(!c){console.error("没有当前对话");return}try{await a(c,C,"user");let A;e==="数据计算"?A=await L(C,s||void 0):A=await B(C,s||void 0),A.output.session_id&&o(A.output.session_id);let I=A.output.text,$=[];if(e==="知识库查询"&&A.output.doc_references&&($=A.output.doc_references,console.log("文档引用:",$)),e==="知识库查询"&&(!$||$.length===0)&&($=[{index_id:"1",doc_name:"AQ3028-2008dz_OCR",title:"化学品生产单位受限空间作业安全规范 4受限空间作业安全要求|4.4通风",text:"4.3.1氧含量一般为18%~21%，在富氧环境下不得大于23.5%。4.3.2 有毒气体(物质)浓度应符合GBZ2的规定。4.3.3可燃气体浓度：当被测气体或蒸气的爆炸下限大于等于4%时，其被测浓度不大于0.5%(体积百分数)；当被测气体或蒸气的爆炸下限小于4%时，其被测浓度不大于0.2%(体积百分数)。",page_number:[2,3],doc_id:"file_7b02efa75e46444abe6132b3771fd9e8_12343237"}],console.log("使用测试文档引用数据")),await a(c,I,"assistant",$),A.usage&&A.usage.models&&A.usage.models[0]){const K=A.usage.models[0].input_tokens+A.usage.models[0].output_tokens;await N(K)}}catch(A){console.error("API调用失败:",A);const I=`抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${A instanceof Error?A.message:"未知错误"}`;await a(c,I,"assistant")}},F=async C=>{try{await navigator.clipboard.writeText(C),console.log("复制成功")}catch(A){console.error("复制失败:",A);const I=document.createElement("textarea");I.value=C,document.body.appendChild(I),I.select(),document.execCommand("copy"),document.body.removeChild(I)}},D=async()=>{if(!b){alert("请先选择一个文件");return}if(!FC(b)){alert("不支持的文件类型。请选择 TXT、PDF 或 DOCX 文件。");return}try{console.log("=== 开始读取文件 ==="),console.log("文件名:",b.name),console.log("文件大小:",b.size,"bytes"),console.log("文件类型:",b.type);const C=await VC(b);console.log("=== 文件内容 ==="),console.log("内容长度:",C.length,"字符"),console.log("文件内容:"),console.log(C),console.log("=== 文件读取完成 ==="),T(C),console.log("=== 开始发送到 DashScope API ==="),await O(C)}catch(C){console.error("处理失败:",C),T(""),alert(`处理失败: ${C instanceof Error?C.message:"未知错误"}`)}};return d.jsx(s0,{isCollapsed:n,onToggle:V,children:d.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[d.jsx("div",{className:"bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx(oe,{variant:"ghost",size:"sm",onClick:V,className:"h-8 w-8 p-0",title:n?"展开侧边栏 (Ctrl+B)":"收起侧边栏 (Ctrl+B)",children:n?d.jsx(Lf,{className:"h-4 w-4"}):d.jsx(Of,{className:"h-4 w-4"})}),d.jsx("div",{className:"flex justify-center flex-1",children:d.jsx("div",{className:"flex w-fit rounded-full bg-muted p-1",children:la.map(C=>d.jsx(Wv,{text:C,selected:e===C,setSelected:m,discount:C==="方案改进"},C))})}),d.jsx(IC,{className:"ml-4"})]})}),d.jsx("main",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:e==="知识库查询"||e==="数据计算"?d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"flex-1 overflow-y-auto p-6 pb-0 min-h-0",children:p.length===0?d.jsxs("div",{className:"text-center text-gray-500 mt-20",children:[e==="知识库查询"?d.jsxs(d.Fragment,{children:[d.jsx("p",{className:"text-lg mb-2",children:"👋 欢迎使用库无忧"}),d.jsx("p",{children:"开始对话，快速检索石化油储行业"})]}):d.jsxs(d.Fragment,{children:[d.jsx("p",{className:"text-lg mb-2",children:"🧮 欢迎使用计算功能"}),d.jsx("p",{children:"开始对话，进行智能计算和分析"})]}),s&&d.jsxs("p",{className:"text-sm mt-4 text-blue-600",children:["🔗 多轮对话已启用 (会话ID: ",s.slice(0,8),"...)"]})]}):d.jsx("div",{className:"max-w-4xl mx-auto space-y-4",children:p.map(C=>{const A=C.role==="user"?"sent":"received";return d.jsxs("div",{className:"space-y-2",children:[C.role==="assistant"&&C.docReferences&&C.docReferences.length>0&&d.jsx(kC,{references:C.docReferences,messageId:C.id}),d.jsxs($C,{variant:A,children:[d.jsx(WC,{src:A==="sent"?"/user.jpg":"/sinochem.png",fallback:A==="sent"?"用户":"AI"}),d.jsxs("div",{className:"flex-1",children:[d.jsx(UC,{variant:A,children:C.content}),C.role==="assistant"&&d.jsx(KC,{children:d.jsx(zC,{icon:d.jsx(Sf,{className:"h-3 w-3"}),onClick:()=>F(C.content)})})]})]})]},C.id)})})}),d.jsx("div",{className:"w-full flex justify-center flex-shrink-0 p-6 pt-0",children:d.jsx("div",{className:"w-full max-w-3xl",children:d.jsx(Bv,{onSend:W,showSuggestedActions:e!=="数据计算"})})})]}):d.jsx(d.Fragment,{children:d.jsx("div",{className:"flex-1 flex flex-col p-6 min-h-0 overflow-hidden",children:M.length===0?d.jsxs("div",{className:"flex-1 flex flex-col justify-center items-center",children:[d.jsxs("div",{className:"text-center text-gray-500 mb-6",children:[d.jsx("p",{className:"text-lg mb-2",children:"📄 方案改进"}),d.jsx("p",{children:"上传文件进行智能分析和优化建议"})]}),d.jsxs("div",{className:"w-full max-w-md space-y-4",children:[d.jsx(Uv,{onFileSelect:E,fileContent:S}),d.jsx(oe,{onClick:D,disabled:!b,className:"w-full",children:"分析文件内容"})]})]}):d.jsxs("div",{className:"w-full max-w-4xl mx-auto h-full flex flex-col",children:[d.jsxs("div",{className:"mb-6 flex items-center justify-between flex-shrink-0",children:[d.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"📋 分析结果"}),d.jsx(oe,{onClick:()=>{P([]),T(""),w(null)},variant:"outline",size:"sm",children:"重新分析"})]}),d.jsx("div",{className:"flex-1 overflow-y-auto space-y-6 pr-2",children:M.map((C,A)=>d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:[d.jsx("div",{className:"mb-4",children:d.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:["问题 ",A+1]})}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-semibold text-blue-700 mb-2",children:"📝 原文内容"}),d.jsx("p",{className:"text-gray-700 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400",children:C.origin})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-semibold text-orange-700 mb-2",children:"⚠️ 问题描述"}),d.jsx("p",{className:"text-gray-700 bg-orange-50 p-3 rounded-md border-l-4 border-orange-400",children:C.issueDes})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-semibold text-green-700 mb-2",children:"💡 改进建议"}),d.jsx("p",{className:"text-gray-700 bg-green-50 p-3 rounded-md border-l-4 border-green-400",children:C.suggestion})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-semibold text-purple-700 mb-2",children:"📚 依据说明"}),d.jsx("p",{className:"text-gray-700 bg-purple-50 p-3 rounded-md border-l-4 border-purple-400",children:C.reason})]})]})]},A))})]})})})})]})})}const lS=function(){return d.jsx(HC,{children:d.jsx(GC,{})})};export{lS as component};
