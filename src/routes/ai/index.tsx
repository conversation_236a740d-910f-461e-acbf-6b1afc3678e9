import { createFileRoute } from "@tanstack/react-router";
import { MultimodalChatInput } from "@/components/ui/multimodal-input";
import { FileUpload } from "@/components/ui/file-upload";
import { Tab } from "@/components/ui/tab";
import { ConversationSidebar } from "@/components/conversation-sidebar";
import { Button } from "@/components/ui/button";
import { UserAvatar } from "@/components/ui/user-avatar";
import { DocReferenceDisplay } from "@/components/ui/doc-reference";
import { PanelLeftClose, PanelLeftOpen, Copy } from "lucide-react";
import { readFileContent, isSupportedFileType } from "@/utils/file-reader";
import {
	ChatBubble,
	ChatBubbleAvatar,
	ChatBubbleMessage,
	ChatBubbleAction,
	ChatBubbleActionWrapper,
} from "@/components/ui/chat-bubble";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useConversationStore } from "@/stores/conversation-store";
import { useUserStore } from "@/stores/user-store";
import { updateUserTokenUsage, updateUserTokenUsageById } from "@/db/user";
import { type User } from "@/db/schema";
import * as React from "react";

export const Route = createFileRoute("/ai/")({
	component: RouteComponent,
});

// AI功能选项
const AI_MODES = ["无忧问答", "无忧分析师", "无忧计算师"];

// 文档引用类型定义
interface DocReference {
	index_id: string;
	doc_name: string;
	text: string;
	page_number?: number[];
	title?: string;
	doc_id?: string;
	images?: any[];
}



// API响应类型定义
interface ApiResponse {
	output: {
		text: string;
		session_id: string;
		finish_reason?: string;
		reject_status?: boolean;
		doc_references?: DocReference[];
	};
	usage: {
		models: Array<{
			model_id: string;
			input_tokens: number;
			output_tokens: number;
		}>;
	};
	request_id: string;
}

/**
 * AI 聊天页面组件
 * 集成了多模态输入功能，支持文本和文件输入
 * 需要用户登录后才能访问
 *
 * 多轮对话功能说明：
 * 1. 首次对话时，不传递session_id，API会返回新的session_id
 * 2. 后续对话时，传递之前获取的session_id，实现上下文连续性
 * 3. 用户可以点击"新对话"按钮清空会话状态，开始新的对话
 * 4. 会话ID会在界面上显示（部分），让用户了解当前对话状态
 *
 * API配置：
 * - 需要在环境变量中设置 DASHSCOPE_API_KEY 和 YOUR_APP_ID
 * - 或者直接在代码中替换相应的常量值
 */
function RouteComponent() {
	return (
		<ProtectedRoute>
			<AIChat />
		</ProtectedRoute>
	);
}

/**
 * AI 聊天主要组件
 * 包含所有聊天功能的实现
 */
function AIChat() {
	// Tab选择状态
	const [selectedMode, setSelectedMode] = React.useState(AI_MODES[0]);
	// 侧边栏状态
	const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false);
	// 会话ID状态，用于多轮对话
	const [sessionId, setSessionId] = React.useState<string | null>(null);

	// 使用conversation store
	const {
		getCurrentConversation,
		addMessage: addMessageToStore,
		currentConversationId,
		createConversation
	} = useConversationStore();



	// 获取当前对话的消息
	const currentConversation = getCurrentConversation();
	const messages = currentConversation?.messages || [];

	// Tab切换处理函数 - 只切换模式，不创建新对话
	const handleTabChange = React.useCallback((newMode: string) => {
		// 更新选中的模式
		setSelectedMode(newMode);

		// 重置会话ID，开始新的对话会话
		setSessionId(null);

		// 清空当前对话选择，这样切换tab时不会显示其他模式的对话
		const { setCurrentConversation } = useConversationStore.getState();
		setCurrentConversation(null);

		console.log(`切换到 ${newMode} 模式`);
	}, []);

	// 移除自动创建对话的逻辑，改为在用户发送第一条消息时创建

	// API配置
	const API_KEY = import.meta.env.VITE_DASHSCOPE_API_KEY || "YOUR_API_KEY";
	const APP_ID = import.meta.env.VITE_YOUR_APP_ID || "YOUR_APP_ID";
	const FILE_APP_ID = import.meta.env.VITE_YOUR_FILE_APP_ID || "YOUR_FILE_APP_ID";
	const CALCULATE_APP_ID = import.meta.env.VITE_CALCULATE_APP_ID || "YOUR_CALCULATE_APP_ID";

	// 文件选择状态（仅用于UI显示）
	const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
	// 文件内容状态（用于在预览框中显示）
	const [fileContent, setFileContent] = React.useState<string>("");
	// API 分析结果状态
	const [analysisResults, setAnalysisResults] = React.useState<any[]>([]);

	// 处理文件选择，同时清空之前的文件内容
	const handleFileSelect = React.useCallback((file: File | null) => {
		setSelectedFile(file);
		setFileContent(""); // 清空之前的文件内容
		setAnalysisResults([]); // 清空之前的分析结果
	}, []);

	// 显示格式化的分析结果
	const displayFormattedResults = (results: any[]) => {
		setAnalysisResults(results);
		console.log("=== 格式化显示结果 ===");
		results.forEach((item, index) => {
			console.log(`%c问题 ${index + 1}:`, 'color: #e74c3c; font-weight: bold;');
			console.log(`%c原文: ${item.origin}`, 'color: #3498db;');
			console.log(`%c问题描述: ${item.issueDes}`, 'color: #f39c12;');
			console.log(`%c改进建议: ${item.suggestion}`, 'color: #27ae60;');
			console.log(`%c依据: ${item.reason}`, 'color: #9b59b6;');
			console.log('---');
		});
	};

	// 更新数据库统计信息
	const updateDatabaseStats = async (tokenUsage: number) => {
		try {
			const { user } = useUserStore.getState();
			if (user && user.id) {
				console.log(`=== 数据库更新 ===`);
				console.log(`Token 使用量: ${tokenUsage}`);
				console.log(`请求次数: +1`);

				// 更新用户的 token 使用量和请求次数
				let updatedUser: User | null;
				if (user.dingTalkUnionId) {
					// 钉钉用户
					updatedUser = await updateUserTokenUsage(user.dingTalkUnionId, tokenUsage);
				} else {
					// 邮箱用户
					updatedUser = await updateUserTokenUsageById(user.id, tokenUsage);
				}

				if (updatedUser) {
					console.log("用户token使用量更新成功:", updatedUser);
					// 更新本地用户状态
					useUserStore.getState().setUser(updatedUser);
				} else {
					console.error("更新用户token使用量失败");
				}
			} else {
				console.warn("用户信息不完整，无法更新数据库");
			}
		} catch (error) {
			console.error("更新数据库失败:", error);
		}
	};

	// 发送文件内容到 DashScope API
	const sendToFileAPI = async (content: string) => {
		try {
			const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/apps/${FILE_APP_ID}/completion`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${API_KEY}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					input: {
						prompt: content
					},
					parameters: {},
					debug: {}
				})
			});

			if (!response.ok) {
				throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
			}

			const data = await response.json();

			console.log("=== DashScope API 响应 ===");
			console.log("完整响应数据:");
			console.log(JSON.stringify(data, null, 2));

			// 解析 text 字段中的 JSON 数据
			if (data.output && data.output.text) {
				try {
					// 提取 JSON 部分（去掉 ```json 和 ``` 标记）
					const jsonText = data.output.text.replace(/```json\n?/, '').replace(/\n?```$/, '');
					const parsedResults = JSON.parse(jsonText);

					console.log("=== 解析后的结果 ===");
					console.log(parsedResults);

					// 显示格式化的结果
					displayFormattedResults(parsedResults);

					// 更新数据库统计信息
					if (data.usage && data.usage.models && data.usage.models[0]) {
						const tokenUsage = data.usage.models[0].input_tokens + data.usage.models[0].output_tokens;
						await updateDatabaseStats(tokenUsage);
					}

				} catch (parseError) {
					console.error("解析 JSON 失败:", parseError);
					console.log("原始 text 内容:", data.output.text);
				}
			}

			console.log("=== API 响应完成 ===");

		} catch (error) {
			console.error("API 请求失败:", error);
			throw error;
		}
	};

	// 切换侧边栏
	const toggleSidebar = () => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	};



	/**
	 * 调用DashScope API进行多轮对话
	 * @param prompt 用户输入的问题
	 * @param currentSessionId 当前会话ID（可选）
	 * @returns API响应
	 */
	const callDashScopeAPI = async (
		prompt: string,
		currentSessionId?: string
	): Promise<ApiResponse> => {
		// 调试：打印API配置信息
		console.log("=== API调用调试信息 ===");
		console.log("API_KEY:", API_KEY);
		console.log("APP_ID:", APP_ID);
		console.log(
			"环境变量 VITE_DASHSCOPE_API_KEY:",
			import.meta.env.VITE_DASHSCOPE_API_KEY
		);
		console.log("环境变量 VITE_YOUR_APP_ID:", import.meta.env.VITE_YOUR_APP_ID);
		console.log("========================");

		const url = `https://dashscope.aliyuncs.com/api/v1/apps/${APP_ID}/completion`;

		const requestBody: any = {
			input: {
				prompt: prompt,
			},
			parameters: {},
			debug: {},
		};

		// 如果有会话ID，添加到请求中以实现多轮对话
		if (currentSessionId) {
			requestBody.input.session_id = currentSessionId;
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${API_KEY}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	};

	/**
	 * 调用DashScope API进行计算功能
	 * @param prompt 用户输入的计算问题
	 * @param currentSessionId 当前会话ID（可选）
	 * @returns API响应
	 */
	const callCalculateAPI = async (
		prompt: string,
		currentSessionId?: string
	): Promise<ApiResponse> => {
		// 调试：打印API配置信息
		console.log("=== 计算API调用调试信息 ===");
		console.log("API_KEY:", API_KEY);
		console.log("CALCULATE_APP_ID:", CALCULATE_APP_ID);
		console.log("环境变量 VITE_CALCULATE_APP_ID:", import.meta.env.VITE_CALCULATE_APP_ID);
		console.log("========================");

		const url = `https://dashscope.aliyuncs.com/api/v1/apps/${CALCULATE_APP_ID}/completion`;

		const requestBody: any = {
			input: {
				prompt: prompt,
			},
			parameters: {},
			debug: {},
		};

		// 如果有会话ID，添加到请求中以实现多轮对话
		if (currentSessionId) {
			requestBody.input.session_id = currentSessionId;
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${API_KEY}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			throw new Error(`计算API请求失败: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	};

	// 发送消息处理函数
	const handleSendMessage = async (content: string) => {
		let conversationId = currentConversationId;

		// 如果没有当前对话，创建新对话
		if (!conversationId) {
			try {
				// 使用用户消息的前8个字符作为对话标题（适合中文显示）
				const title = content.length > 8 ? content.substring(0, 8) + "..." : content;
				conversationId = await createConversation(title, selectedMode);
				console.log(`创建新对话: ${title}`);
			} catch (error) {
				console.error("创建对话失败:", error);
				return;
			}
		}

		try {
			// 添加用户消息到store
			await addMessageToStore(conversationId, content, "user");

			// 根据选择的模式调用不同的API
			let apiResponse: ApiResponse;
			if (selectedMode === "无忧计算师") {
				apiResponse = await callCalculateAPI(
					content,
					sessionId || undefined
				);
			} else {
				// 默认使用知识库查询API（包括"无忧问答"和"无忧分析师"）
				apiResponse = await callDashScopeAPI(
					content,
					sessionId || undefined
				);
			}

			// 更新会话ID（首次对话时获取，后续对话时保持）
			if (apiResponse.output.session_id) {
				setSessionId(apiResponse.output.session_id);
			}

			// 处理无忧问答的文档引用
			let messageContent = apiResponse.output.text;
			let docReferences: DocReference[] = [];

			if (selectedMode === "无忧问答" && apiResponse.output.doc_references) {
				docReferences = apiResponse.output.doc_references;
				console.log("文档引用:", docReferences);
			}

			// 临时测试数据 - 如果是无忧问答但没有文档引用，添加测试数据
			if (selectedMode === "无忧问答" && (!docReferences || docReferences.length === 0)) {
				docReferences = [
					{
						index_id: "1",
						doc_name: "AQ3028-2008dz_OCR",
						title: "化学品生产单位受限空间作业安全规范 4受限空间作业安全要求|4.4通风",
						text: "4.3.1氧含量一般为18%~21%，在富氧环境下不得大于23.5%。4.3.2 有毒气体(物质)浓度应符合GBZ2的规定。4.3.3可燃气体浓度：当被测气体或蒸气的爆炸下限大于等于4%时，其被测浓度不大于0.5%(体积百分数)；当被测气体或蒸气的爆炸下限小于4%时，其被测浓度不大于0.2%(体积百分数)。",
						page_number: [2, 3],
						doc_id: "file_7b02efa75e46444abe6132b3771fd9e8_12343237"
					}
				];
				console.log("使用测试文档引用数据");
			}

			// 添加AI回复消息到store（包含文档引用信息）
			await addMessageToStore(conversationId, messageContent, "assistant", docReferences);

			// 计算并更新token使用量
			if (apiResponse.usage && apiResponse.usage.models && apiResponse.usage.models[0]) {
				const tokenUsage = apiResponse.usage.models[0].input_tokens + apiResponse.usage.models[0].output_tokens;
				await updateDatabaseStats(tokenUsage);
			}

		} catch (error) {
			console.error("API调用失败:", error);
			// 添加错误消息到store
			const errorMessage = `抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${error instanceof Error ? error.message : "未知错误"}`;
			await addMessageToStore(conversationId, errorMessage, "assistant");
		}
	};

	// 复制消息内容
	const handleCopyMessage = async (content: string) => {
		try {
			await navigator.clipboard.writeText(content);
			// 可以在这里添加成功提示，比如 toast 通知
			console.log("复制成功");
		} catch (err) {
			console.error("复制失败:", err);
			// 降级方案：使用传统的复制方法
			const textArea = document.createElement("textarea");
			textArea.value = content;
			document.body.appendChild(textArea);
			textArea.select();
			document.execCommand("copy");
			document.body.removeChild(textArea);
		}
	};

	/**
	 * 处理文件上传并读取文件内容
	 */
	const handleFileUpload = async () => {
		if (!selectedFile) {
			alert("请先选择一个文件");
			return;
		}

		// 检查文件类型是否支持
		if (!isSupportedFileType(selectedFile)) {
			alert("不支持的文件类型。请选择 TXT、PDF 或 DOCX 文件。");
			return;
		}

		try {
			console.log("=== 开始读取文件 ===");
			console.log("文件名:", selectedFile.name);
			console.log("文件大小:", selectedFile.size, "bytes");
			console.log("文件类型:", selectedFile.type);

			// 读取文件内容
			const content = await readFileContent(selectedFile);

			console.log("=== 文件内容 ===");
			console.log("内容长度:", content.length, "字符");
			console.log("文件内容:");
			console.log(content);
			console.log("=== 文件读取完成 ===");

			// 设置文件内容到状态中，用于在预览框中显示
			setFileContent(content);

			// 发送到 DashScope API 进行处理
			console.log("=== 开始发送到 DashScope API ===");
			await sendToFileAPI(content);

		} catch (error) {
			console.error("处理失败:", error);
			setFileContent(""); // 清空内容
			alert(`处理失败: ${error instanceof Error ? error.message : "未知错误"}`);
		}
	};




	return (
		<ConversationSidebar
			isCollapsed={isSidebarCollapsed}
			onToggle={toggleSidebar}
			currentMode={selectedMode}
		>
			<div className="h-screen bg-gray-50 flex flex-col">
				{/* Tab导航区域 - 固定在顶部 */}
				<div className="bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0">
					<div className="flex items-center justify-between">
						{/* 侧边栏切换按钮 */}
						<Button
							variant="ghost"
							size="sm"
							onClick={toggleSidebar}
							className="h-8 w-8 p-0"
							title={
								isSidebarCollapsed
									? "展开侧边栏 (Ctrl+B)"
									: "收起侧边栏 (Ctrl+B)"
							}
						>
							{isSidebarCollapsed ? (
								<PanelLeftOpen className="h-4 w-4" />
							) : (
								<PanelLeftClose className="h-4 w-4" />
							)}
						</Button>

						{/* Tab选择器 */}
						<div className="flex justify-center flex-1">
							<div className="flex w-fit rounded-full bg-muted p-1">
								{AI_MODES.map((mode) => (
									<Tab
										key={mode}
										text={mode}
										selected={selectedMode === mode}
										setSelected={handleTabChange}
										discount={mode === "无忧分析师"}
									/>
								))}
							</div>
						</div>

						{/* 用户头像 */}
						<UserAvatar className="ml-4" />
					</div>
				</div>

				{/* 聊天区域 - 可滚动的主要内容区域 */}
				<main className="flex-1 flex flex-col min-h-0 overflow-hidden">
					{selectedMode === "无忧问答" || selectedMode === "无忧计算师" ? (
						<>
							{/* 消息列表区域 - 可滚动 */}
							<div className="flex-1 overflow-y-auto p-6 pb-0 min-h-0">
								{messages.length === 0 ? (
									<div className="text-center text-gray-500 mt-20">
										{selectedMode === "无忧问答" ? (
											<>
												<p className="text-lg mb-2">👋 欢迎使用无忧问答</p>
												<p>开始对话，快速检索石化油储行业</p>
											</>
										) : (
											<>
												<p className="text-lg mb-2">🧮 欢迎使用无忧计算师</p>
												<p>开始对话，进行智能计算和分析</p>
											</>
										)}
										{sessionId && (
											<p className="text-sm mt-4 text-blue-600">
												🔗 多轮对话已启用 (会话ID: {sessionId.slice(0, 8)}...)
											</p>
										)}
									</div>
								) : (
									<div className="max-w-4xl mx-auto space-y-4">
										{messages.map((message) => {
											const variant =
												message.role === "user" ? "sent" : "received";
											return (
												<div key={message.id} className="space-y-2">
													{/* 文档引用显示（仅对AI消息且有文档引用时显示） */}
													{message.role === "assistant" && message.docReferences && message.docReferences.length > 0 && (
														<DocReferenceDisplay
															references={message.docReferences}
															messageId={message.id}
														/>
													)}

													<ChatBubble variant={variant}>
														<ChatBubbleAvatar
															src={
																variant === "sent"
																	? "/user.jpg"
																	: "/sinochem.png"
															}
															fallback={variant === "sent" ? "用户" : "AI"}
														/>
														<div className="flex-1">
															<ChatBubbleMessage variant={variant}>
																{message.content}
															</ChatBubbleMessage>
															{/* AI消息的操作按钮 */}
															{message.role === "assistant" && (
																<ChatBubbleActionWrapper>
																	<ChatBubbleAction
																		icon={<Copy className="h-3 w-3" />}
																		onClick={() =>
																			handleCopyMessage(message.content)
																		}
																	/>
																</ChatBubbleActionWrapper>
															)}
														</div>
													</ChatBubble>
												</div>
											);
										})}
									</div>
								)}
							</div>

							{/* 输入区域 - 固定在底部 */}
							<div className="w-full flex justify-center flex-shrink-0 p-6 pt-0">
								<div className="w-full max-w-3xl">
									<MultimodalChatInput
										onSend={handleSendMessage}
										showSuggestedActions={selectedMode !== "无忧计算师"}
									/>
								</div>
							</div>
						</>
					) : (
						<>
							{/* 无忧分析师模式 */}
							<div className="flex-1 flex flex-col p-6 min-h-0 overflow-hidden">
								{analysisResults.length === 0 ? (
									<div className="flex-1 flex flex-col justify-center items-center">
										<div className="text-center text-gray-500 mb-6">
											<p className="text-lg mb-2">📄 无忧分析师</p>
											<p>上传文件进行智能分析和优化建议</p>
										</div>
										<div className="w-full max-w-md space-y-4">
											<FileUpload onFileSelect={handleFileSelect} fileContent={fileContent} />

											<Button
												onClick={handleFileUpload}
												disabled={!selectedFile}
												className="w-full"
											>
												分析文件内容
											</Button>
										</div>
									</div>
								) : (
									<div className="w-full max-w-4xl mx-auto h-full flex flex-col">
										<div className="mb-6 flex items-center justify-between flex-shrink-0">
											<h2 className="text-2xl font-bold text-gray-800">📋 分析结果</h2>
											<Button
												onClick={() => {
													setAnalysisResults([]);
													setFileContent("");
													setSelectedFile(null);
												}}
												variant="outline"
												size="sm"
											>
												重新分析
											</Button>
										</div>

										<div className="flex-1 overflow-y-auto space-y-6 pr-2">
											{analysisResults.map((item, index) => (
												<div key={index} className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
													<div className="mb-4">
														<span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
															问题 {index + 1}
														</span>
													</div>

													<div className="space-y-4">
														<div>
															<h4 className="text-sm font-semibold text-blue-700 mb-2">📝 原文内容</h4>
															<p className="text-gray-700 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400">
																{item.origin}
															</p>
														</div>

														<div>
															<h4 className="text-sm font-semibold text-orange-700 mb-2">⚠️ 问题描述</h4>
															<p className="text-gray-700 bg-orange-50 p-3 rounded-md border-l-4 border-orange-400">
																{item.issueDes}
															</p>
														</div>

														<div>
															<h4 className="text-sm font-semibold text-green-700 mb-2">💡 改进建议</h4>
															<p className="text-gray-700 bg-green-50 p-3 rounded-md border-l-4 border-green-400">
																{item.suggestion}
															</p>
														</div>

														<div>
															<h4 className="text-sm font-semibold text-purple-700 mb-2">📚 依据说明</h4>
															<p className="text-gray-700 bg-purple-50 p-3 rounded-md border-l-4 border-purple-400">
																{item.reason}
															</p>
														</div>
													</div>
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						</>
					)}
				</main>
			</div>
		</ConversationSidebar>
	);
}
