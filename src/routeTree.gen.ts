/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as UserRouteImport } from './routes/user'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ModelIndexRouteImport } from './routes/model/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as AiIndexRouteImport } from './routes/ai/index'
import { Route as PolicyServiceRouteImport } from './routes/policy/service'
import { Route as PolicyPrivacyRouteImport } from './routes/policy/privacy'
import { Route as DashboardUserRouteImport } from './routes/dashboard/user'
import { Route as AuthRegisterRouteImport } from './routes/auth/register'
import { Route as AuthLoginRouteImport } from './routes/auth/login'
import { Route as AuthDingtalkCallbackRouteImport } from './routes/auth/dingtalk/callback'
import { ServerRoute as ApiAuthRegisterServerRouteImport } from './routes/api/auth/register'
import { ServerRoute as ApiAuthLoginServerRouteImport } from './routes/api/auth/login'
import { ServerRoute as ApiAuthDingtalkUserinfoServerRouteImport } from './routes/api/auth/dingtalk-userinfo'
import { ServerRoute as ApiAuthDingtalkUserServerRouteImport } from './routes/api/auth/dingtalk-user'
import { ServerRoute as ApiAuthDingtalkCallbackServerRouteImport } from './routes/api/auth/dingtalk-callback'

const rootServerRouteImport = createServerRootRoute()

const UserRoute = UserRouteImport.update({
  id: '/user',
  path: '/user',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ModelIndexRoute = ModelIndexRouteImport.update({
  id: '/model/',
  path: '/model/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => rootRouteImport,
} as any)
const AiIndexRoute = AiIndexRouteImport.update({
  id: '/ai/',
  path: '/ai/',
  getParentRoute: () => rootRouteImport,
} as any)
const PolicyServiceRoute = PolicyServiceRouteImport.update({
  id: '/policy/service',
  path: '/policy/service',
  getParentRoute: () => rootRouteImport,
} as any)
const PolicyPrivacyRoute = PolicyPrivacyRouteImport.update({
  id: '/policy/privacy',
  path: '/policy/privacy',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardUserRoute = DashboardUserRouteImport.update({
  id: '/dashboard/user',
  path: '/dashboard/user',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRegisterRoute = AuthRegisterRouteImport.update({
  id: '/auth/register',
  path: '/auth/register',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthDingtalkCallbackRoute = AuthDingtalkCallbackRouteImport.update({
  id: '/auth/dingtalk/callback',
  path: '/auth/dingtalk/callback',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiAuthRegisterServerRoute = ApiAuthRegisterServerRouteImport.update({
  id: '/api/auth/register',
  path: '/api/auth/register',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiAuthLoginServerRoute = ApiAuthLoginServerRouteImport.update({
  id: '/api/auth/login',
  path: '/api/auth/login',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiAuthDingtalkUserinfoServerRoute =
  ApiAuthDingtalkUserinfoServerRouteImport.update({
    id: '/api/auth/dingtalk-userinfo',
    path: '/api/auth/dingtalk-userinfo',
    getParentRoute: () => rootServerRouteImport,
  } as any)
const ApiAuthDingtalkUserServerRoute =
  ApiAuthDingtalkUserServerRouteImport.update({
    id: '/api/auth/dingtalk-user',
    path: '/api/auth/dingtalk-user',
    getParentRoute: () => rootServerRouteImport,
  } as any)
const ApiAuthDingtalkCallbackServerRoute =
  ApiAuthDingtalkCallbackServerRouteImport.update({
    id: '/api/auth/dingtalk-callback',
    path: '/api/auth/dingtalk-callback',
    getParentRoute: () => rootServerRouteImport,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/user': typeof UserRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/policy/privacy': typeof PolicyPrivacyRoute
  '/policy/service': typeof PolicyServiceRoute
  '/ai': typeof AiIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/model': typeof ModelIndexRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/user': typeof UserRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/policy/privacy': typeof PolicyPrivacyRoute
  '/policy/service': typeof PolicyServiceRoute
  '/ai': typeof AiIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/model': typeof ModelIndexRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/user': typeof UserRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/policy/privacy': typeof PolicyPrivacyRoute
  '/policy/service': typeof PolicyServiceRoute
  '/ai/': typeof AiIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/model/': typeof ModelIndexRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/user'
    | '/auth/login'
    | '/auth/register'
    | '/dashboard/user'
    | '/policy/privacy'
    | '/policy/service'
    | '/ai'
    | '/dashboard'
    | '/model'
    | '/auth/dingtalk/callback'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/user'
    | '/auth/login'
    | '/auth/register'
    | '/dashboard/user'
    | '/policy/privacy'
    | '/policy/service'
    | '/ai'
    | '/dashboard'
    | '/model'
    | '/auth/dingtalk/callback'
  id:
    | '__root__'
    | '/'
    | '/user'
    | '/auth/login'
    | '/auth/register'
    | '/dashboard/user'
    | '/policy/privacy'
    | '/policy/service'
    | '/ai/'
    | '/dashboard/'
    | '/model/'
    | '/auth/dingtalk/callback'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  UserRoute: typeof UserRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthRegisterRoute: typeof AuthRegisterRoute
  DashboardUserRoute: typeof DashboardUserRoute
  PolicyPrivacyRoute: typeof PolicyPrivacyRoute
  PolicyServiceRoute: typeof PolicyServiceRoute
  AiIndexRoute: typeof AiIndexRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  ModelIndexRoute: typeof ModelIndexRoute
  AuthDingtalkCallbackRoute: typeof AuthDingtalkCallbackRoute
}
export interface FileServerRoutesByFullPath {
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackServerRoute
  '/api/auth/dingtalk-user': typeof ApiAuthDingtalkUserServerRoute
  '/api/auth/dingtalk-userinfo': typeof ApiAuthDingtalkUserinfoServerRoute
  '/api/auth/login': typeof ApiAuthLoginServerRoute
  '/api/auth/register': typeof ApiAuthRegisterServerRoute
}
export interface FileServerRoutesByTo {
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackServerRoute
  '/api/auth/dingtalk-user': typeof ApiAuthDingtalkUserServerRoute
  '/api/auth/dingtalk-userinfo': typeof ApiAuthDingtalkUserinfoServerRoute
  '/api/auth/login': typeof ApiAuthLoginServerRoute
  '/api/auth/register': typeof ApiAuthRegisterServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackServerRoute
  '/api/auth/dingtalk-user': typeof ApiAuthDingtalkUserServerRoute
  '/api/auth/dingtalk-userinfo': typeof ApiAuthDingtalkUserinfoServerRoute
  '/api/auth/login': typeof ApiAuthLoginServerRoute
  '/api/auth/register': typeof ApiAuthRegisterServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths:
    | '/api/auth/dingtalk-callback'
    | '/api/auth/dingtalk-user'
    | '/api/auth/dingtalk-userinfo'
    | '/api/auth/login'
    | '/api/auth/register'
  fileServerRoutesByTo: FileServerRoutesByTo
  to:
    | '/api/auth/dingtalk-callback'
    | '/api/auth/dingtalk-user'
    | '/api/auth/dingtalk-userinfo'
    | '/api/auth/login'
    | '/api/auth/register'
  id:
    | '__root__'
    | '/api/auth/dingtalk-callback'
    | '/api/auth/dingtalk-user'
    | '/api/auth/dingtalk-userinfo'
    | '/api/auth/login'
    | '/api/auth/register'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiAuthDingtalkCallbackServerRoute: typeof ApiAuthDingtalkCallbackServerRoute
  ApiAuthDingtalkUserServerRoute: typeof ApiAuthDingtalkUserServerRoute
  ApiAuthDingtalkUserinfoServerRoute: typeof ApiAuthDingtalkUserinfoServerRoute
  ApiAuthLoginServerRoute: typeof ApiAuthLoginServerRoute
  ApiAuthRegisterServerRoute: typeof ApiAuthRegisterServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/user': {
      id: '/user'
      path: '/user'
      fullPath: '/user'
      preLoaderRoute: typeof UserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/model/': {
      id: '/model/'
      path: '/model'
      fullPath: '/model'
      preLoaderRoute: typeof ModelIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/ai/': {
      id: '/ai/'
      path: '/ai'
      fullPath: '/ai'
      preLoaderRoute: typeof AiIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/policy/service': {
      id: '/policy/service'
      path: '/policy/service'
      fullPath: '/policy/service'
      preLoaderRoute: typeof PolicyServiceRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/policy/privacy': {
      id: '/policy/privacy'
      path: '/policy/privacy'
      fullPath: '/policy/privacy'
      preLoaderRoute: typeof PolicyPrivacyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/user': {
      id: '/dashboard/user'
      path: '/dashboard/user'
      fullPath: '/dashboard/user'
      preLoaderRoute: typeof DashboardUserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/register': {
      id: '/auth/register'
      path: '/auth/register'
      fullPath: '/auth/register'
      preLoaderRoute: typeof AuthRegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/dingtalk/callback': {
      id: '/auth/dingtalk/callback'
      path: '/auth/dingtalk/callback'
      fullPath: '/auth/dingtalk/callback'
      preLoaderRoute: typeof AuthDingtalkCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/auth/register': {
      id: '/api/auth/register'
      path: '/api/auth/register'
      fullPath: '/api/auth/register'
      preLoaderRoute: typeof ApiAuthRegisterServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/auth/login': {
      id: '/api/auth/login'
      path: '/api/auth/login'
      fullPath: '/api/auth/login'
      preLoaderRoute: typeof ApiAuthLoginServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/auth/dingtalk-userinfo': {
      id: '/api/auth/dingtalk-userinfo'
      path: '/api/auth/dingtalk-userinfo'
      fullPath: '/api/auth/dingtalk-userinfo'
      preLoaderRoute: typeof ApiAuthDingtalkUserinfoServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/auth/dingtalk-user': {
      id: '/api/auth/dingtalk-user'
      path: '/api/auth/dingtalk-user'
      fullPath: '/api/auth/dingtalk-user'
      preLoaderRoute: typeof ApiAuthDingtalkUserServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/auth/dingtalk-callback': {
      id: '/api/auth/dingtalk-callback'
      path: '/api/auth/dingtalk-callback'
      fullPath: '/api/auth/dingtalk-callback'
      preLoaderRoute: typeof ApiAuthDingtalkCallbackServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  UserRoute: UserRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthRegisterRoute: AuthRegisterRoute,
  DashboardUserRoute: DashboardUserRoute,
  PolicyPrivacyRoute: PolicyPrivacyRoute,
  PolicyServiceRoute: PolicyServiceRoute,
  AiIndexRoute: AiIndexRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  ModelIndexRoute: ModelIndexRoute,
  AuthDingtalkCallbackRoute: AuthDingtalkCallbackRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiAuthDingtalkCallbackServerRoute: ApiAuthDingtalkCallbackServerRoute,
  ApiAuthDingtalkUserServerRoute: ApiAuthDingtalkUserServerRoute,
  ApiAuthDingtalkUserinfoServerRoute: ApiAuthDingtalkUserinfoServerRoute,
  ApiAuthLoginServerRoute: ApiAuthLoginServerRoute,
  ApiAuthRegisterServerRoute: ApiAuthRegisterServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
